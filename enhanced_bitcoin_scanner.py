#!/usr/bin/env python3
"""
Enhanced Bitcoin Key Scanner with Pattern-Based Optimization
ماسح البيتكوين المحسن مع التحسين القائم على الأنماط

This script generates Bitcoin keys using specific patterns that may increase
the probability of finding addresses with balances.
"""

import hashlib
import base58
import ecdsa
import requests
import time
import json
import random
import threading
import logging
import os
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
import argparse
import sys

class PatternBasedBitcoinScanner:
    def __init__(self, pattern_type="sequential", batch_size=1000, duration_minutes=60, 
                 output_file="found_keys_enhanced.txt", log_level="INFO"):
        """
        Initialize the enhanced Bitcoin scanner with pattern-based optimization
        
        Args:
            pattern_type: Type of pattern to use for key generation
            batch_size: Number of keys to generate in each batch
            duration_minutes: How long to run the scanner
            output_file: File to save found keys
            log_level: Logging level (DEBUG, INFO, WARNING, ERROR)
        """
        self.pattern_type = pattern_type
        self.batch_size = batch_size
        self.duration = timedelta(minutes=duration_minutes)
        self.output_file = output_file
        self.start_time = datetime.now()
        self.keys_checked = 0
        self.found_keys = 0
        self.running = True
        
        # API settings
        self.api_delay = 0.05  # 50ms delay between API calls
        self.max_workers = 5   # Number of concurrent threads
        
        # Setup logging
        self.setup_logging(log_level)
        
        # Pattern configurations
        self.patterns = {
            "sequential": self.generate_sequential_keys,
            "vanity_1": self.generate_vanity_1_keys,
            "vanity_3": self.generate_vanity_3_keys,
            "repeated_digits": self.generate_repeated_digit_keys,
            "low_entropy": self.generate_low_entropy_keys,
            "birthday_dates": self.generate_birthday_keys,
            "common_numbers": self.generate_common_number_keys,
            "hex_patterns": self.generate_hex_pattern_keys
        }
        
        self.logger.info(f"Enhanced Scanner initialized:")
        self.logger.info(f"Pattern: {pattern_type}")
        self.logger.info(f"Batch size: {batch_size}")
        self.logger.info(f"Duration: {duration_minutes} minutes")
        self.logger.info(f"Output file: {output_file}")
        self.logger.info("-" * 50)

    def setup_logging(self, log_level):
        """Setup logging configuration"""
        # Create logs directory if it doesn't exist
        if not os.path.exists('logs'):
            os.makedirs('logs')
        
        # Configure logging
        log_filename = f"logs/bitcoin_scanner_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        
        logging.basicConfig(
            level=getattr(logging, log_level.upper()),
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_filename),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger(__name__)

    def generate_sequential_keys(self, batch_size):
        """Generate sequential keys in specific ranges"""
        keys = []
        # Focus on early Bitcoin era ranges (2009-2012)
        ranges = [
            (1, 1000000),                    # Very early keys
            (0x1000, 0x100000),             # Early hex range
            (0x123456, 0x1234567),          # Sequential patterns
            (0xABCDEF, 0xABCDEFF),          # Hex patterns
        ]
        
        for _ in range(batch_size):
            range_start, range_end = random.choice(ranges)
            key = random.randint(range_start, range_end)
            keys.append(key)
        
        return keys

    def generate_vanity_1_keys(self, batch_size):
        """Generate keys that might produce addresses starting with '1A', '1B', etc."""
        keys = []
        # These ranges are more likely to produce vanity addresses
        vanity_ranges = [
            (0x1A000000, 0x1AFFFFFF),
            (0x1B000000, 0x1BFFFFFF),
            (0x1C000000, 0x1CFFFFFF),
            (0x1D000000, 0x1DFFFFFF),
            (0x1E000000, 0x1EFFFFFF),
        ]
        
        for _ in range(batch_size):
            range_start, range_end = random.choice(vanity_ranges)
            key = random.randint(range_start, range_end)
            keys.append(key)
        
        return keys

    def generate_vanity_3_keys(self, batch_size):
        """Generate keys for P2SH addresses (starting with '3')"""
        keys = []
        # Ranges that might produce P2SH addresses
        p2sh_ranges = [
            (0x3A000000, 0x3AFFFFFF),
            (0x3B000000, 0x3BFFFFFF),
            (0x30000000, 0x3FFFFFFF),
        ]
        
        for _ in range(batch_size):
            range_start, range_end = random.choice(p2sh_ranges)
            key = random.randint(range_start, range_end)
            keys.append(key)
        
        return keys

    def generate_repeated_digit_keys(self, batch_size):
        """Generate keys with repeated digits or patterns"""
        keys = []
        patterns = [
            0x1111111111111111,
            0x2222222222222222,
            0x1234567890ABCDEF,
            0xAAAAAAAAAAAAAAAA,
            0x1010101010101010,
            0x123123123123123,
            0xABCABCABCABCABC,
        ]
        
        for _ in range(batch_size):
            base_pattern = random.choice(patterns)
            # Add some variation to the pattern
            variation = random.randint(0, 0xFFFF)
            key = (base_pattern + variation) % (2**256 - 1)
            keys.append(key)
        
        return keys

    def generate_low_entropy_keys(self, batch_size):
        """Generate keys with low entropy (common in early Bitcoin days)"""
        keys = []
        
        for _ in range(batch_size):
            # Generate keys with limited randomness
            entropy_bits = random.choice([8, 16, 24, 32, 40, 48])
            max_value = 2**entropy_bits - 1
            key = random.randint(1, max_value)
            keys.append(key)
        
        return keys

    def generate_birthday_keys(self, batch_size):
        """Generate keys based on common birthday dates"""
        keys = []
        
        for _ in range(batch_size):
            # Common date formats as hex
            year = random.randint(1970, 2020)
            month = random.randint(1, 12)
            day = random.randint(1, 28)
            
            # Convert date to various numeric formats
            date_formats = [
                int(f"{year}{month:02d}{day:02d}"),
                int(f"{day:02d}{month:02d}{year}"),
                int(f"{month:02d}{day:02d}{year}"),
                year * 10000 + month * 100 + day,
            ]
            
            base_key = random.choice(date_formats)
            # Add some padding to make it a valid private key
            key = (base_key * 0x1000000000000) % (2**256 - 1)
            if key == 0:
                key = base_key
            keys.append(key)
        
        return keys

    def generate_common_number_keys(self, batch_size):
        """Generate keys based on common numbers and sequences"""
        keys = []
        common_numbers = [
            123456789, 987654321, 111111111, 999999999,
            1234567890, 1111111111, 2222222222,
            12345, 54321, 11111, 99999, 77777,
            1337, 2020, 2021, 2022, 2023, 2024,
            42, 69, 420, 666, 777, 888, 999,
        ]
        
        for _ in range(batch_size):
            base_num = random.choice(common_numbers)
            # Create variations
            multiplier = random.choice([1, 10, 100, 1000, 10000, 100000])
            key = (base_num * multiplier) % (2**256 - 1)
            if key == 0:
                key = base_num
            keys.append(key)
        
        return keys

    def generate_hex_pattern_keys(self, batch_size):
        """Generate keys with specific hex patterns"""
        keys = []
        hex_patterns = [
            "DEADBEEF", "CAFEBABE", "FEEDFACE", "BADCAFE",
            "12345678", "87654321", "ABCDEFAB", "FEDCBA98",
            "11111111", "AAAAAAAA", "FFFFFFFF", "00000000",
        ]
        
        for _ in range(batch_size):
            pattern = random.choice(hex_patterns)
            # Repeat pattern to fill 256 bits
            full_pattern = (pattern * 8)[:64]  # 64 hex chars = 256 bits
            key = int(full_pattern, 16)
            keys.append(key)
        
        return keys

    def private_key_to_wif(self, private_key_int):
        """Convert private key integer to Wallet Import Format (WIF)"""
        try:
            # Add version byte (0x80 for mainnet)
            extended_key = b'\x80' + private_key_int.to_bytes(32, 'big')
            
            # Add compression flag (0x01 for compressed)
            extended_key += b'\x01'
            
            # Calculate checksum
            checksum = hashlib.sha256(hashlib.sha256(extended_key).digest()).digest()[:4]
            
            # Encode in base58
            return base58.b58encode(extended_key + checksum).decode()
        except Exception as e:
            self.logger.error(f"Error converting to WIF: {e}")
            return None

    def private_key_to_address(self, private_key_int):
        """Convert private key to Bitcoin address"""
        try:
            # Create private key object
            private_key_bytes = private_key_int.to_bytes(32, 'big')
            signing_key = ecdsa.SigningKey.from_string(private_key_bytes, curve=ecdsa.SECP256k1)
            
            # Get public key
            verifying_key = signing_key.get_verifying_key()
            public_key_bytes = b'\x02' + verifying_key.to_string()[:32]  # Compressed public key
            
            # Hash public key
            sha256_hash = hashlib.sha256(public_key_bytes).digest()
            ripemd160_hash = hashlib.new('ripemd160', sha256_hash).digest()
            
            # Add version byte (0x00 for mainnet)
            versioned_hash = b'\x00' + ripemd160_hash
            
            # Calculate checksum
            checksum = hashlib.sha256(hashlib.sha256(versioned_hash).digest()).digest()[:4]
            
            # Encode address
            address = base58.b58encode(versioned_hash + checksum).decode()
            
            return address
            
        except Exception as e:
            self.logger.error(f"Error generating address: {e}")
            return None

    def check_balance(self, address):
        """Check balance of a Bitcoin address"""
        try:
            time.sleep(self.api_delay)
            
            url = f"https://blockchain.info/q/addressbalance/{address}"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                balance_satoshi = int(response.text)
                balance_btc = balance_satoshi / 100000000
                return balance_btc
            else:
                return None
                
        except Exception as e:
            self.logger.debug(f"Error checking balance for {address}: {e}")
            return None

    def process_key_batch(self, keys_batch):
        """Process a batch of keys"""
        results = []
        
        for private_key in keys_batch:
            if not self.running:
                break
                
            address = self.private_key_to_address(private_key)
            if not address:
                continue
            
            self.logger.debug(f"Checking key: {hex(private_key)} -> {address}")
            
            balance = self.check_balance(address)
            
            result = {
                'private_key': private_key,
                'address': address,
                'balance': balance,
                'has_balance': balance is not None and balance > 0
            }
            
            results.append(result)
            self.keys_checked += 1
            
            if result['has_balance']:
                self.found_keys += 1
                self.save_found_key(private_key, address, balance)
                self.logger.info(f"🎉 FOUND KEY WITH BALANCE! Address: {address}, Balance: {balance} BTC")
        
        return results

    def save_found_key(self, private_key_int, address, balance):
        """Save found key with balance to file"""
        try:
            wif = self.private_key_to_wif(private_key_int)
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            with open(self.output_file, 'a') as f:
                f.write(f"Timestamp: {timestamp}\n")
                f.write(f"Pattern Type: {self.pattern_type}\n")
                f.write(f"Private Key (Hex): {hex(private_key_int)}\n")
                f.write(f"Private Key (WIF): {wif}\n")
                f.write(f"Address: {address}\n")
                f.write(f"Balance: {balance} BTC\n")
                f.write("-" * 60 + "\n")

        except Exception as e:
            self.logger.error(f"Error saving key: {e}")

    def print_stats(self):
        """Print current statistics"""
        elapsed = datetime.now() - self.start_time
        remaining = self.duration - elapsed

        if remaining.total_seconds() > 0:
            keys_per_second = self.keys_checked / elapsed.total_seconds() if elapsed.total_seconds() > 0 else 0

            stats_msg = (f"Keys checked: {self.keys_checked:,} | "
                        f"Found: {self.found_keys} | "
                        f"Rate: {keys_per_second:.2f} keys/sec | "
                        f"Pattern: {self.pattern_type} | "
                        f"Time remaining: {str(remaining).split('.')[0]}")

            print(f"\r{stats_msg}", end="")
            self.logger.debug(stats_msg)
        else:
            self.running = False

    def run_batch_processing(self):
        """Main batch processing loop"""
        self.logger.info("Starting enhanced Bitcoin key scanner with pattern-based optimization...")
        self.logger.info(f"Using pattern: {self.pattern_type}")
        self.logger.info("Press Ctrl+C to stop early\n")

        batch_count = 0

        try:
            while self.running and datetime.now() - self.start_time < self.duration:
                batch_count += 1
                self.logger.info(f"Processing batch {batch_count} ({self.batch_size} keys)...")

                # Generate keys using selected pattern
                if self.pattern_type in self.patterns:
                    keys_batch = self.patterns[self.pattern_type](self.batch_size)
                else:
                    self.logger.error(f"Unknown pattern type: {self.pattern_type}")
                    break

                # Process batch with threading for better performance
                with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                    # Split batch into smaller chunks for threading
                    chunk_size = max(1, self.batch_size // self.max_workers)
                    chunks = [keys_batch[i:i + chunk_size] for i in range(0, len(keys_batch), chunk_size)]

                    # Submit chunks to thread pool
                    future_to_chunk = {executor.submit(self.process_key_batch, chunk): chunk for chunk in chunks}

                    # Collect results
                    for future in as_completed(future_to_chunk):
                        try:
                            results = future.result()
                            # Results are already processed in process_key_batch
                        except Exception as e:
                            self.logger.error(f"Error processing chunk: {e}")

                # Print stats after each batch
                self.print_stats()
                print()  # New line after stats

                self.logger.info(f"Batch {batch_count} completed. Keys checked so far: {self.keys_checked:,}")

                # Small delay between batches to avoid overwhelming the API
                time.sleep(1)

        except KeyboardInterrupt:
            self.logger.info("\nStopped by user")
        except Exception as e:
            self.logger.error(f"Error during scanning: {e}")

        self.print_final_stats()

    def print_final_stats(self):
        """Print final statistics"""
        elapsed = datetime.now() - self.start_time
        avg_rate = self.keys_checked / elapsed.total_seconds() if elapsed.total_seconds() > 0 else 0

        print(f"\n{'='*60}")
        print(f"SCAN COMPLETED!")
        print(f"{'='*60}")
        print(f"Pattern used: {self.pattern_type}")
        print(f"Total runtime: {str(elapsed).split('.')[0]}")
        print(f"Total keys checked: {self.keys_checked:,}")
        print(f"Keys with balance found: {self.found_keys}")
        print(f"Average rate: {avg_rate:.2f} keys/second")
        print(f"Results saved to: {self.output_file}")
        print(f"Log file saved to: logs/")

        if self.found_keys > 0:
            print(f"\n🎉 SUCCESS! Found {self.found_keys} key(s) with balance!")
        else:
            print(f"\n💡 No keys with balance found in this run.")
            print(f"   Try different patterns or longer duration.")

        print(f"{'='*60}")

    def run(self):
        """Main entry point"""
        self.run_batch_processing()


def main():
    parser = argparse.ArgumentParser(description="Enhanced Bitcoin Key Scanner with Pattern Optimization")

    parser.add_argument("--pattern", type=str, default="sequential",
                       choices=["sequential", "vanity_1", "vanity_3", "repeated_digits",
                               "low_entropy", "birthday_dates", "common_numbers", "hex_patterns"],
                       help="Pattern type for key generation")

    parser.add_argument("--batch-size", type=int, default=1000,
                       help="Number of keys to generate in each batch")

    parser.add_argument("--duration", type=float, default=60,
                       help="Duration to run scanner (minutes)")

    parser.add_argument("--output", type=str, default="found_keys_enhanced.txt",
                       help="Output file for found keys")

    parser.add_argument("--log-level", type=str, default="INFO",
                       choices=["DEBUG", "INFO", "WARNING", "ERROR"],
                       help="Logging level")

    parser.add_argument("--workers", type=int, default=5,
                       help="Number of concurrent workers")

    args = parser.parse_args()

    # Create and run scanner
    scanner = PatternBasedBitcoinScanner(
        pattern_type=args.pattern,
        batch_size=args.batch_size,
        duration_minutes=args.duration,
        output_file=args.output,
        log_level=args.log_level
    )

    scanner.max_workers = args.workers
    scanner.run()


if __name__ == "__main__":
    main()
