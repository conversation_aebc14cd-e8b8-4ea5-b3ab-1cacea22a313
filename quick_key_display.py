#!/usr/bin/env python3
"""
Quick Key Display - عرض سريع للمفاتيح
عرض مبسط وسريع للمفاتيح ومحتواها في الكونسول
"""

import hashlib
import base58
import ecdsa
import requests
import time
import random
import argparse
from datetime import datetime
from enhanced_bitcoin_scanner import PatternBasedBitcoinScanner

class QuickKeyDisplay:
    def __init__(self):
        """Initialize the quick key display"""
        self.scanner = PatternBasedBitcoinScanner(
            pattern_type="low_entropy",
            batch_size=10,
            duration_minutes=1
        )

    def display_key_simple(self, private_key_int, key_number=1):
        """Display key information in a simple format"""
        print(f"\n{'='*60}")
        print(f"🔑 KEY #{key_number:03d} | المفتاح رقم {key_number}")
        print(f"{'='*60}")
        
        # Generate key information
        address = self.scanner.private_key_to_address(private_key_int)
        wif = self.scanner.private_key_to_wif(private_key_int)
        
        # Display basic info
        print(f"🔢 Private Key (Dec): {private_key_int:,}")
        print(f"🔤 Private Key (Hex): {hex(private_key_int)}")
        print(f"💼 WIF: {wif}")
        print(f"🏠 Address: {address}")
        
        # Quick analysis
        hex_len = len(hex(private_key_int)) - 2
        if hex_len <= 8:
            entropy = "Very Low"
        elif hex_len <= 16:
            entropy = "Low"
        else:
            entropy = "Medium+"
        
        print(f"🎲 Entropy: {entropy} ({hex_len} hex chars)")
        
        # Address type
        if address:
            if address.startswith('1'):
                addr_type = "Legacy (P2PKH)"
            elif address.startswith('3'):
                addr_type = "Script (P2SH)"
            else:
                addr_type = "Unknown"
            print(f"🏷️ Type: {addr_type}")
        
        # Balance check
        print(f"💰 Checking balance...")
        balance = self.scanner.check_balance(address)
        
        if balance is not None:
            if balance > 0:
                print(f"🎉 BALANCE FOUND: {balance} BTC")
                satoshi = int(balance * 100000000)
                print(f"💰 Satoshi: {satoshi:,}")
                return True
            else:
                print(f"💸 No balance (0 BTC)")
        else:
            print(f"❌ Could not check balance")
        
        print(f"⏰ Generated: {datetime.now().strftime('%H:%M:%S')}")
        return False

    def display_key_compact(self, private_key_int, key_number=1):
        """Display key in ultra-compact format"""
        address = self.scanner.private_key_to_address(private_key_int)
        wif = self.scanner.private_key_to_wif(private_key_int)
        
        print(f"#{key_number:03d} | {hex(private_key_int):>20s} | {address} | ", end="")
        
        # Quick balance check
        balance = self.scanner.check_balance(address)
        if balance is not None and balance > 0:
            print(f"💰 {balance} BTC")
            return True
        else:
            print("💸 0 BTC")
            return False

    def display_key_table(self, private_key_int, key_number=1):
        """Display key in table format"""
        address = self.scanner.private_key_to_address(private_key_int)
        wif = self.scanner.private_key_to_wif(private_key_int)
        
        # Balance check
        balance = self.scanner.check_balance(address)
        balance_str = f"{balance} BTC" if balance and balance > 0 else "0 BTC"
        
        print(f"| {key_number:3d} | {hex(private_key_int):>18s} | {address:34s} | {balance_str:>12s} |")
        
        return balance and balance > 0

    def generate_and_display(self, pattern="low_entropy", count=5, format_type="simple"):
        """Generate and display keys in specified format"""
        
        print(f"\n🚀 Bitcoin Key Generator - Quick Display")
        print(f"Pattern: {pattern} | Count: {count} | Format: {format_type}")
        print(f"{'='*80}")
        
        # Generate keys
        if pattern in self.scanner.patterns:
            keys = self.scanner.patterns[pattern](count)
        else:
            print(f"❌ Unknown pattern: {pattern}")
            return
        
        found_keys = 0
        
        # Table header for table format
        if format_type == "table":
            print(f"| {'#':3s} | {'Private Key (Hex)':>18s} | {'Bitcoin Address':34s} | {'Balance':>12s} |")
            print(f"|{'-'*5}|{'-'*20}|{'-'*36}|{'-'*14}|")
        
        # Display keys
        for i, key in enumerate(keys, 1):
            try:
                if format_type == "simple":
                    has_balance = self.display_key_simple(key, i)
                elif format_type == "compact":
                    has_balance = self.display_key_compact(key, i)
                elif format_type == "table":
                    has_balance = self.display_key_table(key, i)
                else:
                    has_balance = self.display_key_simple(key, i)
                
                if has_balance:
                    found_keys += 1
                
                # Small delay to avoid overwhelming API
                if i < len(keys):
                    time.sleep(0.1)
                
            except KeyboardInterrupt:
                print(f"\n⏹️ Stopped by user")
                break
            except Exception as e:
                print(f"❌ Error processing key {i}: {e}")
                continue
        
        # Summary
        print(f"\n{'='*80}")
        print(f"📊 SUMMARY: {found_keys}/{len(keys)} keys with balance found")
        if found_keys > 0:
            print(f"🎉 SUCCESS! Found {found_keys} key(s) with balance!")
        print(f"{'='*80}")

def main():
    """Main function with command line arguments"""
    parser = argparse.ArgumentParser(description="Quick Bitcoin Key Display")
    
    parser.add_argument("--pattern", type=str, default="low_entropy",
                       choices=["sequential", "vanity_1", "vanity_3", "repeated_digits", 
                               "low_entropy", "birthday_dates", "common_numbers", "hex_patterns"],
                       help="Pattern type for key generation")
    
    parser.add_argument("--count", type=int, default=5,
                       help="Number of keys to generate (1-50)")
    
    parser.add_argument("--format", type=str, default="simple",
                       choices=["simple", "compact", "table"],
                       help="Display format")
    
    parser.add_argument("--no-balance-check", action="store_true",
                       help="Skip balance checking for faster display")
    
    args = parser.parse_args()
    
    # Limit count to reasonable number
    count = min(max(args.count, 1), 50)
    
    display = QuickKeyDisplay()
    
    # If no balance check, modify the display methods
    if args.no_balance_check:
        def quick_display_no_balance(private_key_int, key_number=1):
            address = display.scanner.private_key_to_address(private_key_int)
            wif = display.scanner.private_key_to_wif(private_key_int)
            
            if args.format == "compact":
                print(f"#{key_number:03d} | {hex(private_key_int):>20s} | {address}")
            elif args.format == "table":
                print(f"| {key_number:3d} | {hex(private_key_int):>18s} | {address:34s} | {'N/A':>12s} |")
            else:
                print(f"\n🔑 KEY #{key_number:03d}")
                print(f"🔤 Hex: {hex(private_key_int)}")
                print(f"🏠 Address: {address}")
                print(f"💼 WIF: {wif}")
            
            return False
        
        # Replace display methods
        display.display_key_simple = quick_display_no_balance
        display.display_key_compact = quick_display_no_balance
        display.display_key_table = quick_display_no_balance
    
    # Generate and display
    display.generate_and_display(args.pattern, count, args.format)

def interactive_mode():
    """Interactive mode for quick key display"""
    display = QuickKeyDisplay()
    
    print("🎮 Quick Key Display - Interactive Mode")
    print("عرض سريع للمفاتيح - الوضع التفاعلي")
    print("="*50)
    
    while True:
        try:
            print("\nOptions:")
            print("1. Generate 5 low entropy keys")
            print("2. Generate 10 common number keys")
            print("3. Generate 3 vanity keys")
            print("4. Custom generation")
            print("5. Table view (10 keys)")
            print("6. Compact view (20 keys)")
            print("7. Exit")
            
            choice = input("\nSelect (1-7): ").strip()
            
            if choice == '1':
                display.generate_and_display("low_entropy", 5, "simple")
            elif choice == '2':
                display.generate_and_display("common_numbers", 10, "simple")
            elif choice == '3':
                display.generate_and_display("vanity_1", 3, "simple")
            elif choice == '4':
                patterns = list(display.scanner.patterns.keys())
                print(f"Available patterns: {', '.join(patterns)}")
                pattern = input("Pattern: ").strip()
                count = int(input("Count (1-20): ") or "5")
                format_type = input("Format (simple/compact/table): ").strip() or "simple"
                
                if pattern in patterns:
                    display.generate_and_display(pattern, min(count, 20), format_type)
                else:
                    print("❌ Invalid pattern")
            elif choice == '5':
                display.generate_and_display("sequential", 10, "table")
            elif choice == '6':
                display.generate_and_display("repeated_digits", 20, "compact")
            elif choice == '7':
                print("👋 Goodbye!")
                break
            else:
                print("❌ Invalid choice")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except ValueError:
            print("❌ Please enter a valid number")
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) == 1:
        # No arguments, run interactive mode
        interactive_mode()
    else:
        # Run with command line arguments
        main()
