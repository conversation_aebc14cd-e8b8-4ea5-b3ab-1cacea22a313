#!/usr/bin/env python3
"""
Console Key Viewer - عارض المفاتيح في الكونسول
يعرض المفاتيح المولدة ومحتواها بشكل مفصل في الكونسول
"""

import hashlib
import base58
import ecdsa
import requests
import time
import random
import json
from datetime import datetime
from enhanced_bitcoin_scanner import PatternBasedBitcoinScanner

class ConsoleKeyViewer:
    def __init__(self):
        """Initialize the console key viewer"""
        self.scanner = PatternBasedBitcoinScanner(
            pattern_type="low_entropy",
            batch_size=10,
            duration_minutes=1
        )
        
        # Colors for console output
        self.colors = {
            'header': '\033[95m',
            'blue': '\033[94m',
            'cyan': '\033[96m',
            'green': '\033[92m',
            'yellow': '\033[93m',
            'red': '\033[91m',
            'bold': '\033[1m',
            'underline': '\033[4m',
            'end': '\033[0m'
        }

    def print_colored(self, text, color='end'):
        """Print colored text"""
        print(f"{self.colors.get(color, '')}{text}{self.colors['end']}")

    def print_separator(self, char='=', length=80):
        """Print a separator line"""
        self.print_colored(char * length, 'cyan')

    def print_key_header(self, key_number):
        """Print header for each key"""
        self.print_separator('=', 80)
        self.print_colored(f"🔑 KEY #{key_number:03d} - معلومات المفتاح رقم {key_number}", 'header')
        self.print_separator('=', 80)

    def display_key_details(self, private_key_int, key_number):
        """Display detailed information about a key"""
        try:
            # Print key header
            self.print_key_header(key_number)
            
            # Generate key information
            address = self.scanner.private_key_to_address(private_key_int)
            wif = self.scanner.private_key_to_wif(private_key_int)
            
            # Basic key information
            self.print_colored("📊 BASIC INFORMATION - المعلومات الأساسية", 'bold')
            self.print_separator('-', 50)
            
            print(f"🔢 Private Key (Decimal): {private_key_int:,}")
            print(f"🔢 المفتاح الخاص (عشري): {private_key_int:,}")
            print()
            
            print(f"🔤 Private Key (Hex): {hex(private_key_int)}")
            print(f"🔤 المفتاح الخاص (hex): {hex(private_key_int)}")
            print()
            
            print(f"📝 Private Key (Binary): {bin(private_key_int)}")
            print(f"📝 المفتاح الخاص (ثنائي): {bin(private_key_int)}")
            print()
            
            if wif:
                print(f"💼 WIF Format: {wif}")
                print(f"💼 تنسيق WIF: {wif}")
            else:
                self.print_colored("❌ Failed to generate WIF", 'red')
            print()
            
            if address:
                print(f"🏠 Bitcoin Address: {address}")
                print(f"🏠 عنوان البيتكوين: {address}")
            else:
                self.print_colored("❌ Failed to generate address", 'red')
                return
            
            print()
            
            # Key analysis
            self.print_colored("🔍 KEY ANALYSIS - تحليل المفتاح", 'bold')
            self.print_separator('-', 50)
            
            # Key length analysis
            hex_length = len(hex(private_key_int)) - 2  # Remove '0x'
            binary_length = len(bin(private_key_int)) - 2  # Remove '0b'
            
            print(f"📏 Hex Length: {hex_length} characters")
            print(f"📏 طول Hex: {hex_length} حرف")
            print()
            
            print(f"📏 Binary Length: {binary_length} bits")
            print(f"📏 طول Binary: {binary_length} بت")
            print()
            
            # Entropy analysis
            entropy_level = self.analyze_entropy(private_key_int)
            print(f"🎲 Entropy Level: {entropy_level}")
            print(f"🎲 مستوى الإنتروبيا: {entropy_level}")
            print()
            
            # Pattern analysis
            pattern_info = self.analyze_patterns(private_key_int)
            print(f"🔍 Pattern Analysis: {pattern_info}")
            print(f"🔍 تحليل الأنماط: {pattern_info}")
            print()
            
            # Address analysis
            self.print_colored("🏠 ADDRESS ANALYSIS - تحليل العنوان", 'bold')
            self.print_separator('-', 50)
            
            address_type = self.analyze_address_type(address)
            print(f"🏷️ Address Type: {address_type}")
            print(f"🏷️ نوع العنوان: {address_type}")
            print()
            
            address_prefix = address[:2] if len(address) >= 2 else address
            print(f"🔤 Address Prefix: {address_prefix}")
            print(f"🔤 بادئة العنوان: {address_prefix}")
            print()
            
            # Balance check
            self.print_colored("💰 BALANCE CHECK - فحص الرصيد", 'bold')
            self.print_separator('-', 50)
            
            print("🔍 Checking balance... فحص الرصيد...")
            balance = self.scanner.check_balance(address)
            
            if balance is not None:
                if balance > 0:
                    self.print_colored(f"🎉 BALANCE FOUND! تم العثور على رصيد!", 'green')
                    self.print_colored(f"💰 Balance: {balance} BTC", 'green')
                    self.print_colored(f"💰 الرصيد: {balance} بيتكوين", 'green')
                    
                    # Convert to other units
                    satoshi = int(balance * 100000000)
                    usd_estimate = balance * 45000  # Rough estimate
                    
                    print(f"💰 Balance (Satoshi): {satoshi:,}")
                    print(f"💰 الرصيد (ساتوشي): {satoshi:,}")
                    print(f"💵 Estimated USD: ${usd_estimate:,.2f}")
                    print(f"💵 تقدير بالدولار: ${usd_estimate:,.2f}")
                else:
                    self.print_colored("💸 No balance (0 BTC)", 'yellow')
                    self.print_colored("💸 لا يوجد رصيد (0 بيتكوين)", 'yellow')
            else:
                self.print_colored("❌ Could not check balance", 'red')
                self.print_colored("❌ لا يمكن فحص الرصيد", 'red')
            
            print()
            
            # Technical details
            self.print_colored("⚙️ TECHNICAL DETAILS - التفاصيل التقنية", 'bold')
            self.print_separator('-', 50)
            
            # Generate public key for display
            try:
                private_key_bytes = private_key_int.to_bytes(32, 'big')
                signing_key = ecdsa.SigningKey.from_string(private_key_bytes, curve=ecdsa.SECP256k1)
                verifying_key = signing_key.get_verifying_key()
                public_key_uncompressed = b'\x04' + verifying_key.to_string()
                public_key_compressed = b'\x02' + verifying_key.to_string()[:32]
                
                print(f"🔑 Public Key (Compressed): {public_key_compressed.hex()}")
                print(f"🔑 المفتاح العام (مضغوط): {public_key_compressed.hex()}")
                print()
                
                print(f"🔑 Public Key (Uncompressed): {public_key_uncompressed.hex()}")
                print(f"🔑 المفتاح العام (غير مضغوط): {public_key_uncompressed.hex()}")
                print()
                
            except Exception as e:
                self.print_colored(f"❌ Error generating public key: {e}", 'red')
            
            # Hash information
            key_hash = hashlib.sha256(str(private_key_int).encode()).hexdigest()
            print(f"🔐 SHA256 Hash: {key_hash}")
            print(f"🔐 تشفير SHA256: {key_hash}")
            print()
            
            # Timestamp
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            print(f"⏰ Generated at: {timestamp}")
            print(f"⏰ تم التوليد في: {timestamp}")
            
            print()
            self.print_separator('=', 80)
            print()
            
            return balance and balance > 0
            
        except Exception as e:
            self.print_colored(f"❌ Error displaying key details: {e}", 'red')
            return False

    def analyze_entropy(self, private_key_int):
        """Analyze the entropy level of the key"""
        hex_str = hex(private_key_int)[2:]  # Remove '0x'
        
        if len(hex_str) <= 8:
            return "Very Low (منخفض جداً)"
        elif len(hex_str) <= 16:
            return "Low (منخفض)"
        elif len(hex_str) <= 32:
            return "Medium (متوسط)"
        elif len(hex_str) <= 48:
            return "High (عالي)"
        else:
            return "Very High (عالي جداً)"

    def analyze_patterns(self, private_key_int):
        """Analyze patterns in the key"""
        hex_str = hex(private_key_int)[2:]
        
        patterns = []
        
        # Check for repeated characters
        if len(set(hex_str)) <= 3:
            patterns.append("Repeated chars (أحرف متكررة)")
        
        # Check for sequential patterns
        if "123" in hex_str or "abc" in hex_str:
            patterns.append("Sequential (متسلسل)")
        
        # Check for common patterns
        common_patterns = ["dead", "beef", "cafe", "babe", "face"]
        for pattern in common_patterns:
            if pattern in hex_str.lower():
                patterns.append(f"Contains '{pattern}' (يحتوي على '{pattern}')")
        
        if not patterns:
            patterns.append("No obvious patterns (لا توجد أنماط واضحة)")
        
        return ", ".join(patterns)

    def analyze_address_type(self, address):
        """Analyze the type of Bitcoin address"""
        if address.startswith('1'):
            return "P2PKH (Legacy) - عنوان تقليدي"
        elif address.startswith('3'):
            return "P2SH (Script Hash) - عنوان سكريبت"
        elif address.startswith('bc1'):
            return "Bech32 (SegWit) - عنوان SegWit"
        else:
            return "Unknown type - نوع غير معروف"

    def generate_and_display_keys(self, pattern_type="low_entropy", count=5):
        """Generate and display multiple keys"""
        self.print_colored("🚀 BITCOIN KEY GENERATOR & VIEWER", 'header')
        self.print_colored("مولد وعارض مفاتيح البيتكوين", 'header')
        self.print_separator('=', 80)
        
        print(f"🔍 Pattern Type: {pattern_type}")
        print(f"🔍 نوع النمط: {pattern_type}")
        print(f"📊 Number of keys: {count}")
        print(f"📊 عدد المفاتيح: {count}")
        print()
        
        # Generate keys using the selected pattern
        if pattern_type in self.scanner.patterns:
            keys = self.scanner.patterns[pattern_type](count)
        else:
            self.print_colored(f"❌ Unknown pattern: {pattern_type}", 'red')
            return
        
        found_keys = 0
        
        for i, key in enumerate(keys, 1):
            try:
                has_balance = self.display_key_details(key, i)
                if has_balance:
                    found_keys += 1
                
                # Pause between keys for readability
                if i < len(keys):
                    input("Press Enter to continue to next key... اضغط Enter للمتابعة...")
                    print()
                
            except KeyboardInterrupt:
                self.print_colored("\n⏹️ Stopped by user - توقف بواسطة المستخدم", 'yellow')
                break
            except Exception as e:
                self.print_colored(f"❌ Error processing key {i}: {e}", 'red')
                continue
        
        # Summary
        self.print_colored("📊 SUMMARY - الملخص", 'header')
        self.print_separator('=', 80)
        print(f"✅ Keys processed: {i}")
        print(f"✅ المفاتيح المعالجة: {i}")
        print(f"💰 Keys with balance: {found_keys}")
        print(f"💰 المفاتيح التي تحتوي على رصيد: {found_keys}")
        
        if found_keys > 0:
            self.print_colored(f"🎉 SUCCESS! Found {found_keys} key(s) with balance!", 'green')
        else:
            self.print_colored("💡 No keys with balance found in this session.", 'yellow')
        
        self.print_separator('=', 80)

def main():
    """Main function with interactive menu"""
    viewer = ConsoleKeyViewer()
    
    while True:
        try:
            viewer.print_colored("🎮 CONSOLE KEY VIEWER MENU", 'header')
            viewer.print_colored("قائمة عارض المفاتيح في الكونسول", 'header')
            viewer.print_separator('=', 50)
            
            print("1. Generate Low Entropy Keys (مفاتيح بإنتروبيا منخفضة)")
            print("2. Generate Common Number Keys (مفاتيح أرقام شائعة)")
            print("3. Generate Sequential Keys (مفاتيح متسلسلة)")
            print("4. Generate Vanity Keys (مفاتيح مميزة)")
            print("5. Generate Random Pattern (نمط عشوائي)")
            print("6. Custom Pattern (نمط مخصص)")
            print("7. Exit (خروج)")
            print()
            
            choice = input("Select option (1-7): ").strip()
            
            if choice == '1':
                count = int(input("Number of keys to generate (1-20): ") or "5")
                viewer.generate_and_display_keys("low_entropy", min(count, 20))
            
            elif choice == '2':
                count = int(input("Number of keys to generate (1-20): ") or "5")
                viewer.generate_and_display_keys("common_numbers", min(count, 20))
            
            elif choice == '3':
                count = int(input("Number of keys to generate (1-20): ") or "5")
                viewer.generate_and_display_keys("sequential", min(count, 20))
            
            elif choice == '4':
                count = int(input("Number of keys to generate (1-20): ") or "5")
                viewer.generate_and_display_keys("vanity_1", min(count, 20))
            
            elif choice == '5':
                patterns = list(viewer.scanner.patterns.keys())
                pattern = random.choice(patterns)
                count = int(input("Number of keys to generate (1-20): ") or "5")
                print(f"Selected random pattern: {pattern}")
                viewer.generate_and_display_keys(pattern, min(count, 20))
            
            elif choice == '6':
                print("Available patterns:")
                for i, pattern in enumerate(viewer.scanner.patterns.keys(), 1):
                    print(f"  {i}. {pattern}")
                
                pattern_choice = input("Enter pattern name: ").strip()
                if pattern_choice in viewer.scanner.patterns:
                    count = int(input("Number of keys to generate (1-20): ") or "5")
                    viewer.generate_and_display_keys(pattern_choice, min(count, 20))
                else:
                    viewer.print_colored("❌ Invalid pattern name", 'red')
            
            elif choice == '7':
                viewer.print_colored("👋 Goodbye! وداعاً!", 'green')
                break
            
            else:
                viewer.print_colored("❌ Invalid choice. Please try again.", 'red')
            
            print()
            
        except KeyboardInterrupt:
            viewer.print_colored("\n👋 Goodbye! وداعاً!", 'green')
            break
        except ValueError:
            viewer.print_colored("❌ Please enter a valid number.", 'red')
        except Exception as e:
            viewer.print_colored(f"❌ Error: {e}", 'red')

if __name__ == "__main__":
    main()
