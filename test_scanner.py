#!/usr/bin/env python3
"""
اختبار وظائف Bitcoin Key Scanner
Test script for Bitcoin Key Scanner functionality
"""

import sys
import os
from bitcoin_key_scanner import BitcoinKeyScanner

def test_key_generation():
    """اختبار توليد المفاتيح"""
    print("اختبار توليد المفاتيح...")
    
    scanner = BitcoinKeyScanner(start_range="1", end_range="FFFF", duration_minutes=1)
    
    # اختبار توليد 5 مفاتيح
    for i in range(5):
        private_key = scanner.generate_private_key()
        address = scanner.private_key_to_address(private_key)
        wif = scanner.private_key_to_wif(private_key)
        
        print(f"Key {i+1}:")
        print(f"  Private Key: {hex(private_key)}")
        print(f"  Address: {address}")
        print(f"  WIF: {wif}")
        print()
    
    print("✅ اختبار توليد المفاتيح مكتمل\n")

def test_address_validation():
    """اختبار صحة العناوين المولدة"""
    print("اختبار صحة العناوين...")
    
    scanner = BitcoinKeyScanner(start_range="1", end_range="100", duration_minutes=1)
    
    # اختبار مع مفاتيح معروفة
    test_keys = [1, 2, 3, 0x123456789ABCDEF]
    
    for key in test_keys:
        address = scanner.private_key_to_address(key)
        if address and address.startswith('1') and len(address) >= 26:
            print(f"✅ Key {hex(key)}: {address}")
        else:
            print(f"❌ Key {hex(key)}: Invalid address")
    
    print("✅ اختبار صحة العناوين مكتمل\n")

def test_api_connection():
    """اختبار الاتصال بـ API"""
    print("اختبار الاتصال بـ blockchain.info API...")
    
    scanner = BitcoinKeyScanner(start_range="1", end_range="100", duration_minutes=1)
    
    # اختبار مع عنوان معروف (Genesis block address)
    test_address = "**********************************"
    
    try:
        balance = scanner.check_balance(test_address)
        if balance is not None:
            print(f"✅ API يعمل بشكل صحيح")
            print(f"   عنوان الاختبار: {test_address}")
            print(f"   الرصيد: {balance} BTC")
        else:
            print("⚠️ API لا يستجيب أو هناك مشكلة في الشبكة")
    except Exception as e:
        print(f"❌ خطأ في الاتصال بـ API: {e}")
    
    print("✅ اختبار API مكتمل\n")

def test_file_operations():
    """اختبار عمليات الملفات"""
    print("اختبار عمليات حفظ الملفات...")
    
    scanner = BitcoinKeyScanner(
        start_range="1", 
        end_range="100", 
        duration_minutes=1,
        output_file="test_output.txt"
    )
    
    # اختبار حفظ مفتاح وهمي
    test_key = 12345
    test_address = "1TestAddress123456789"
    test_balance = 0.001
    
    try:
        scanner.save_found_key(test_key, test_address, test_balance)
        
        # التحقق من وجود الملف
        if os.path.exists("test_output.txt"):
            print("✅ تم إنشاء ملف النتائج بنجاح")
            
            # قراءة المحتوى
            with open("test_output.txt", 'r') as f:
                content = f.read()
                if test_address in content:
                    print("✅ تم حفظ البيانات بشكل صحيح")
                else:
                    print("❌ البيانات لم تُحفظ بشكل صحيح")
            
            # حذف ملف الاختبار
            os.remove("test_output.txt")
            print("✅ تم حذف ملف الاختبار")
        else:
            print("❌ لم يتم إنشاء ملف النتائج")
            
    except Exception as e:
        print(f"❌ خطأ في عمليات الملفات: {e}")
    
    print("✅ اختبار عمليات الملفات مكتمل\n")

def run_quick_scan():
    """تشغيل فحص سريع للاختبار"""
    print("تشغيل فحص سريع (10 ثوانٍ)...")
    
    scanner = BitcoinKeyScanner(
        start_range="1",
        end_range="1000",
        duration_minutes=0.17,  # 10 ثوانٍ تقريباً
        output_file="quick_scan_results.txt"
    )
    
    try:
        scanner.run()
        print("✅ الفحص السريع مكتمل")
    except Exception as e:
        print(f"❌ خطأ في الفحص السريع: {e}")
    
    print()

def main():
    """تشغيل جميع الاختبارات"""
    print("=" * 50)
    print("Bitcoin Key Scanner - اختبارات النظام")
    print("=" * 50)
    print()
    
    try:
        test_key_generation()
        test_address_validation()
        test_api_connection()
        test_file_operations()
        
        # اختبار اختياري للفحص السريع
        response = input("هل تريد تشغيل فحص سريع؟ (y/n): ")
        if response.lower() in ['y', 'yes', 'نعم']:
            run_quick_scan()
        
        print("=" * 50)
        print("✅ جميع الاختبارات مكتملة!")
        print("السكربت جاهز للاستخدام.")
        print("=" * 50)
        
    except KeyboardInterrupt:
        print("\n\nتم إيقاف الاختبارات بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ عام في الاختبارات: {e}")

if __name__ == "__main__":
    main()
