#!/usr/bin/env python3
"""
Test script for Enhanced Bitcoin Pattern Scanner
سكربت اختبار لماسح البيتكوين المحسن القائم على الأنماط
"""

import sys
import os
import time
from enhanced_bitcoin_scanner import PatternBasedBitcoinScanner

def test_pattern_generation():
    """Test all pattern generation methods"""
    print("🧪 Testing Pattern Generation Methods")
    print("اختبار طرق توليد الأنماط")
    print("=" * 50)
    
    scanner = PatternBasedBitcoinScanner(
        pattern_type="sequential",
        batch_size=10,
        duration_minutes=1
    )
    
    patterns_to_test = [
        "sequential",
        "vanity_1", 
        "vanity_3",
        "repeated_digits",
        "low_entropy",
        "birthday_dates",
        "common_numbers",
        "hex_patterns"
    ]
    
    results = {}
    
    for pattern_name in patterns_to_test:
        print(f"\n🔍 Testing pattern: {pattern_name}")
        
        try:
            if pattern_name in scanner.patterns:
                keys = scanner.patterns[pattern_name](5)  # Generate 5 test keys
                
                print(f"✅ Generated {len(keys)} keys")
                print(f"   Sample keys: {[hex(k) for k in keys[:3]]}")
                
                # Test address generation for first key
                if keys:
                    address = scanner.private_key_to_address(keys[0])
                    wif = scanner.private_key_to_wif(keys[0])
                    
                    if address and wif:
                        print(f"   Sample address: {address}")
                        print(f"   Sample WIF: {wif[:20]}...")
                        results[pattern_name] = "✅ PASS"
                    else:
                        print(f"   ❌ Failed to generate address/WIF")
                        results[pattern_name] = "❌ FAIL"
                else:
                    print(f"   ❌ No keys generated")
                    results[pattern_name] = "❌ FAIL"
            else:
                print(f"   ❌ Pattern not found in scanner")
                results[pattern_name] = "❌ FAIL"
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
            results[pattern_name] = f"❌ ERROR: {str(e)[:30]}"
    
    # Summary
    print(f"\n{'='*50}")
    print("PATTERN GENERATION TEST RESULTS:")
    print("نتائج اختبار توليد الأنماط:")
    print("-" * 50)
    
    for pattern, result in results.items():
        print(f"{pattern:20s}: {result}")
    
    passed = sum(1 for r in results.values() if "✅" in r)
    total = len(results)
    print(f"\nPassed: {passed}/{total}")
    print("=" * 50)
    
    return passed == total

def test_address_generation():
    """Test Bitcoin address generation"""
    print("\n🔑 Testing Bitcoin Address Generation")
    print("اختبار توليد عناوين البيتكوين")
    print("=" * 50)
    
    scanner = PatternBasedBitcoinScanner(
        pattern_type="sequential",
        batch_size=10,
        duration_minutes=1
    )
    
    # Test with known values
    test_keys = [
        1,
        2,
        3,
        0x123456789ABCDEF,
        0x1111111111111111,
        0xDEADBEEFCAFEBABE
    ]
    
    success_count = 0
    
    for i, key in enumerate(test_keys, 1):
        print(f"\n🔍 Test {i}: Key {hex(key)}")
        
        try:
            address = scanner.private_key_to_address(key)
            wif = scanner.private_key_to_wif(key)
            
            if address and wif:
                print(f"   ✅ Address: {address}")
                print(f"   ✅ WIF: {wif}")
                
                # Basic validation
                if address.startswith('1') and len(address) >= 26:
                    print(f"   ✅ Address format valid")
                    success_count += 1
                else:
                    print(f"   ❌ Address format invalid")
            else:
                print(f"   ❌ Failed to generate address or WIF")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print(f"\nAddress generation success rate: {success_count}/{len(test_keys)}")
    return success_count == len(test_keys)

def test_api_connection():
    """Test API connection (without overwhelming the service)"""
    print("\n🌐 Testing API Connection")
    print("اختبار الاتصال بـ API")
    print("=" * 50)
    
    scanner = PatternBasedBitcoinScanner(
        pattern_type="sequential",
        batch_size=10,
        duration_minutes=1
    )
    
    # Test with a known address (Genesis block address)
    test_address = "**********************************"
    
    print(f"🔍 Testing with address: {test_address}")
    
    try:
        balance = scanner.check_balance(test_address)
        
        if balance is not None:
            print(f"✅ API connection successful")
            print(f"   Balance: {balance} BTC")
            return True
        else:
            print(f"⚠️ API connection failed or no response")
            return False
            
    except Exception as e:
        print(f"❌ API connection error: {e}")
        return False

def test_batch_processing():
    """Test batch processing functionality"""
    print("\n📦 Testing Batch Processing")
    print("اختبار معالجة الدفعات")
    print("=" * 50)
    
    scanner = PatternBasedBitcoinScanner(
        pattern_type="sequential",
        batch_size=20,
        duration_minutes=0.1,  # Very short duration
        log_level="WARNING"    # Reduce log noise
    )
    
    print("🔍 Running short batch processing test...")
    
    try:
        # Generate a small batch
        keys = scanner.patterns["sequential"](10)
        
        print(f"✅ Generated batch of {len(keys)} keys")
        
        # Test processing (without API calls to avoid rate limiting)
        processed_count = 0
        for key in keys[:3]:  # Test only first 3
            address = scanner.private_key_to_address(key)
            if address:
                processed_count += 1
        
        print(f"✅ Successfully processed {processed_count}/3 test keys")
        
        if processed_count == 3:
            print("✅ Batch processing test PASSED")
            return True
        else:
            print("❌ Batch processing test FAILED")
            return False
            
    except Exception as e:
        print(f"❌ Batch processing error: {e}")
        return False

def test_file_operations():
    """Test file operations"""
    print("\n📁 Testing File Operations")
    print("اختبار عمليات الملفات")
    print("=" * 50)
    
    scanner = PatternBasedBitcoinScanner(
        pattern_type="sequential",
        batch_size=10,
        duration_minutes=1,
        output_file="test_output_patterns.txt"
    )
    
    # Test saving a found key
    test_key = 12345
    test_address = "1TestAddress123456789"
    test_balance = 0.001
    
    try:
        print("🔍 Testing save functionality...")
        scanner.save_found_key(test_key, test_address, test_balance)
        
        # Check if file was created
        if os.path.exists("test_output_patterns.txt"):
            print("✅ Output file created successfully")
            
            # Read and verify content
            with open("test_output_patterns.txt", 'r') as f:
                content = f.read()
                
            if test_address in content and str(test_balance) in content:
                print("✅ File content is correct")
                
                # Clean up
                os.remove("test_output_patterns.txt")
                print("✅ Test file cleaned up")
                return True
            else:
                print("❌ File content is incorrect")
                return False
        else:
            print("❌ Output file was not created")
            return False
            
    except Exception as e:
        print(f"❌ File operations error: {e}")
        return False

def test_logging():
    """Test logging functionality"""
    print("\n📝 Testing Logging System")
    print("اختبار نظام السجلات")
    print("=" * 50)
    
    try:
        # Test different log levels
        for log_level in ["DEBUG", "INFO", "WARNING", "ERROR"]:
            print(f"🔍 Testing log level: {log_level}")
            
            scanner = PatternBasedBitcoinScanner(
                pattern_type="sequential",
                batch_size=5,
                duration_minutes=0.01,
                log_level=log_level
            )
            
            scanner.logger.info(f"Test log message for {log_level}")
        
        # Check if logs directory exists
        if os.path.exists('logs'):
            print("✅ Logs directory created")
            
            # Check for log files
            log_files = [f for f in os.listdir('logs') if f.startswith('bitcoin_scanner_')]
            if log_files:
                print(f"✅ Log files created: {len(log_files)} files")
                return True
            else:
                print("⚠️ No log files found")
                return False
        else:
            print("❌ Logs directory not created")
            return False
            
    except Exception as e:
        print(f"❌ Logging error: {e}")
        return False

def run_comprehensive_test():
    """Run all tests"""
    print("🚀 Enhanced Bitcoin Scanner - Comprehensive Test Suite")
    print("مجموعة الاختبارات الشاملة لماسح البيتكوين المحسن")
    print("=" * 60)
    
    tests = [
        ("Pattern Generation", test_pattern_generation),
        ("Address Generation", test_address_generation),
        ("API Connection", test_api_connection),
        ("Batch Processing", test_batch_processing),
        ("File Operations", test_file_operations),
        ("Logging System", test_logging)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"Running: {test_name}")
        print("=" * 60)
        
        try:
            result = test_func()
            results[test_name] = "✅ PASS" if result else "❌ FAIL"
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results[test_name] = "❌ ERROR"
        
        time.sleep(1)  # Brief pause between tests
    
    # Final summary
    print(f"\n{'='*60}")
    print("COMPREHENSIVE TEST RESULTS:")
    print("نتائج الاختبارات الشاملة:")
    print("=" * 60)
    
    for test_name, result in results.items():
        print(f"{test_name:20s}: {result}")
    
    passed = sum(1 for r in results.values() if "✅" in r)
    total = len(results)
    
    print(f"\nOverall Result: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! The scanner is ready to use.")
        print("🎉 جميع الاختبارات نجحت! الماسح جاهز للاستخدام.")
    else:
        print("⚠️ Some tests failed. Please check the issues above.")
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة المشاكل أعلاه.")
    
    print("=" * 60)

def main():
    """Main test runner"""
    if len(sys.argv) > 1:
        test_name = sys.argv[1].lower()
        
        test_map = {
            "patterns": test_pattern_generation,
            "addresses": test_address_generation,
            "api": test_api_connection,
            "batch": test_batch_processing,
            "files": test_file_operations,
            "logging": test_logging,
            "all": run_comprehensive_test
        }
        
        if test_name in test_map:
            test_map[test_name]()
        else:
            print(f"Unknown test: {test_name}")
            print(f"Available tests: {', '.join(test_map.keys())}")
    else:
        run_comprehensive_test()

if __name__ == "__main__":
    main()
