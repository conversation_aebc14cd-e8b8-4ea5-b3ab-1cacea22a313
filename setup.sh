#!/bin/bash

# Bitcoin Key Scanner Setup Script
# سكربت إعداد ماسح مفاتيح البيتكوين

echo "=================================================="
echo "Bitcoin Key Scanner - Setup Script"
echo "سكربت إعداد ماسح مفاتيح البيتكوين"
echo "=================================================="
echo

# Check if Python 3 is installed
echo "Checking Python installation..."
if command -v python3 &> /dev/null; then
    PYTHON_VERSION=$(python3 --version)
    echo "✅ Python found: $PYTHON_VERSION"
else
    echo "❌ Python 3 is not installed. Please install Python 3.6 or higher."
    exit 1
fi

# Check if pip is installed
echo "Checking pip installation..."
if command -v pip3 &> /dev/null; then
    echo "✅ pip3 found"
    PIP_CMD="pip3"
elif command -v pip &> /dev/null; then
    echo "✅ pip found"
    PIP_CMD="pip"
else
    echo "❌ pip is not installed. Please install pip."
    exit 1
fi

# Install requirements
echo
echo "Installing Python dependencies..."
echo "تثبيت المتطلبات..."

if $PIP_CMD install -r requirements.txt; then
    echo "✅ Dependencies installed successfully"
    echo "✅ تم تثبيت المتطلبات بنجاح"
else
    echo "❌ Failed to install dependencies"
    echo "❌ فشل في تثبيت المتطلبات"
    exit 1
fi

# Make scripts executable
echo
echo "Making scripts executable..."
chmod +x bitcoin_key_scanner.py
chmod +x example_usage.py
chmod +x test_scanner.py

# Run tests
echo
echo "Running system tests..."
echo "تشغيل اختبارات النظام..."

if python3 test_scanner.py; then
    echo "✅ System tests completed"
else
    echo "⚠️ Some tests may have failed, but the scanner should still work"
fi

echo
echo "=================================================="
echo "Setup completed! الإعداد مكتمل!"
echo "=================================================="
echo
echo "Usage examples:"
echo "أمثلة الاستخدام:"
echo
echo "1. Basic scan (فحص أساسي):"
echo "   python3 bitcoin_key_scanner.py"
echo
echo "2. Custom range (نطاق مخصص):"
echo "   python3 bitcoin_key_scanner.py --start 1 --end FFFF --duration 10"
echo
echo "3. Run examples (تشغيل الأمثلة):"
echo "   python3 example_usage.py"
echo
echo "4. View help (عرض المساعدة):"
echo "   python3 bitcoin_key_scanner.py --help"
echo
echo "=================================================="
