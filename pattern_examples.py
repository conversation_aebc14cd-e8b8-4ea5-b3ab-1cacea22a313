#!/usr/bin/env python3
"""
Pattern-Based Bitcoin Scanner Examples
أمثلة ماسح البيتكوين القائم على الأنماط

This script demonstrates different pattern-based scanning strategies
to optimize the probability of finding Bitcoin addresses with balances.
"""

import json
import time
from enhanced_bitcoin_scanner import PatternBasedBitcoinScanner

def load_pattern_config():
    """Load pattern configuration from JSON file"""
    try:
        with open('pattern_config.json', 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print("Pattern config file not found. Using default settings.")
        return {}

def run_pattern_example(pattern_name, config, duration_minutes=5):
    """Run a specific pattern example"""
    print(f"\n{'='*60}")
    print(f"Running Pattern: {pattern_name.upper()}")
    print(f"{'='*60}")
    
    # Get pattern info from config
    pattern_info = config.get('patterns', {}).get(pattern_name, {})
    
    if pattern_info:
        print(f"Description: {pattern_info.get('description', 'N/A')}")
        print(f"Arabic: {pattern_info.get('description_ar', 'N/A')}")
        print(f"Probability: {pattern_info.get('probability', 'Unknown')}")
    
    print(f"Duration: {duration_minutes} minutes")
    print(f"Batch size: 1000 keys per batch")
    print("-" * 60)
    
    # Create scanner with specific pattern
    scanner = PatternBasedBitcoinScanner(
        pattern_type=pattern_name,
        batch_size=1000,
        duration_minutes=duration_minutes,
        output_file=f"results_{pattern_name}.txt",
        log_level="INFO"
    )
    
    # Run the scanner
    scanner.run()
    
    print(f"\nPattern {pattern_name} completed!")
    return scanner.found_keys

def demo_all_patterns():
    """Demonstrate all available patterns with short runs"""
    config = load_pattern_config()
    
    print("🔍 Bitcoin Pattern Scanner Demo")
    print("عرض توضيحي لماسح أنماط البيتكوين")
    print("=" * 60)
    
    patterns = [
        "low_entropy",      # High probability
        "common_numbers",   # High probability  
        "sequential",       # Medium probability
        "vanity_1",        # High probability
        "repeated_digits",  # Medium probability
        "birthday_dates",   # Medium probability
        "hex_patterns",     # Medium probability
        "vanity_3"         # Medium probability
    ]
    
    total_found = 0
    
    for i, pattern in enumerate(patterns, 1):
        print(f"\n[{i}/{len(patterns)}] Testing pattern: {pattern}")
        
        try:
            found = run_pattern_example(pattern, config, duration_minutes=2)
            total_found += found
            
            print(f"✅ Pattern {pattern} completed. Found: {found} keys")
            
            # Short break between patterns
            time.sleep(3)
            
        except KeyboardInterrupt:
            print(f"\n⏹️ Demo stopped by user at pattern: {pattern}")
            break
        except Exception as e:
            print(f"❌ Error running pattern {pattern}: {e}")
            continue
    
    print(f"\n{'='*60}")
    print(f"DEMO COMPLETED!")
    print(f"Total keys with balance found: {total_found}")
    print(f"{'='*60}")

def run_recommended_strategy(strategy_name="intermediate"):
    """Run a recommended scanning strategy"""
    config = load_pattern_config()
    strategies = config.get('recommended_scanning_strategies', {})
    
    if strategy_name not in strategies:
        print(f"Strategy '{strategy_name}' not found. Available strategies:")
        for name in strategies.keys():
            print(f"  - {name}")
        return
    
    strategy = strategies[strategy_name]
    
    print(f"\n🎯 Running Recommended Strategy: {strategy_name.upper()}")
    print(f"{'='*60}")
    print(f"Pattern: {strategy['pattern']}")
    print(f"Batch size: {strategy['batch_size']}")
    print(f"Duration: {strategy['duration']} minutes")
    print(f"Workers: {strategy['workers']}")
    print("-" * 60)
    
    scanner = PatternBasedBitcoinScanner(
        pattern_type=strategy['pattern'],
        batch_size=strategy['batch_size'],
        duration_minutes=strategy['duration'],
        output_file=f"results_{strategy_name}_strategy.txt",
        log_level="INFO"
    )
    
    scanner.max_workers = strategy['workers']
    scanner.run()

def interactive_pattern_selection():
    """Interactive pattern selection menu"""
    config = load_pattern_config()
    patterns = config.get('patterns', {})
    
    print("\n🔧 Interactive Pattern Selection")
    print("اختيار الأنماط التفاعلي")
    print("=" * 60)
    
    # Display available patterns
    pattern_list = list(patterns.keys())
    for i, pattern in enumerate(pattern_list, 1):
        pattern_info = patterns[pattern]
        prob = pattern_info.get('probability', 'Unknown')
        desc = pattern_info.get('description', 'N/A')
        print(f"{i:2d}. {pattern:15s} [{prob:6s}] - {desc}")
    
    print(f"{len(pattern_list)+1:2d}. all_patterns      [Demo  ] - Run all patterns (short demo)")
    print(f"{len(pattern_list)+2:2d}. exit              [Exit  ] - Exit program")
    
    while True:
        try:
            choice = input(f"\nSelect pattern (1-{len(pattern_list)+2}): ").strip()
            
            if choice == str(len(pattern_list)+2) or choice.lower() == 'exit':
                print("Goodbye! وداعاً!")
                break
            elif choice == str(len(pattern_list)+1) or choice.lower() == 'all':
                demo_all_patterns()
                break
            else:
                choice_idx = int(choice) - 1
                if 0 <= choice_idx < len(pattern_list):
                    selected_pattern = pattern_list[choice_idx]
                    
                    # Get duration
                    duration = input("Duration in minutes (default 10): ").strip()
                    duration = float(duration) if duration else 10.0
                    
                    # Get batch size
                    batch_size = input("Batch size (default 1000): ").strip()
                    batch_size = int(batch_size) if batch_size else 1000
                    
                    print(f"\n🚀 Starting scan with pattern: {selected_pattern}")
                    
                    scanner = PatternBasedBitcoinScanner(
                        pattern_type=selected_pattern,
                        batch_size=batch_size,
                        duration_minutes=duration,
                        output_file=f"results_{selected_pattern}_interactive.txt",
                        log_level="INFO"
                    )
                    
                    scanner.run()
                    break
                else:
                    print("Invalid choice. Please try again.")
        
        except ValueError:
            print("Please enter a valid number.")
        except KeyboardInterrupt:
            print("\nExiting...")
            break

def quick_test_all_patterns():
    """Quick test of all patterns (30 seconds each)"""
    config = load_pattern_config()
    patterns = list(config.get('patterns', {}).keys())
    
    print("\n⚡ Quick Test - All Patterns (30 seconds each)")
    print("اختبار سريع - جميع الأنماط (30 ثانية لكل نمط)")
    print("=" * 60)
    
    results = {}
    
    for pattern in patterns:
        print(f"\n🔍 Testing: {pattern}")
        
        try:
            scanner = PatternBasedBitcoinScanner(
                pattern_type=pattern,
                batch_size=100,  # Smaller batch for quick test
                duration_minutes=0.5,  # 30 seconds
                output_file=f"quick_test_{pattern}.txt",
                log_level="WARNING"  # Less verbose
            )
            
            scanner.run()
            results[pattern] = scanner.found_keys
            
        except Exception as e:
            print(f"Error testing {pattern}: {e}")
            results[pattern] = 0
    
    # Summary
    print(f"\n{'='*60}")
    print("QUICK TEST RESULTS:")
    print("نتائج الاختبار السريع:")
    print("-" * 60)
    
    for pattern, found in results.items():
        status = "✅" if found > 0 else "⭕"
        print(f"{status} {pattern:20s}: {found} keys found")
    
    total_found = sum(results.values())
    print(f"\nTotal keys with balance found: {total_found}")
    print("=" * 60)

def main():
    """Main menu"""
    print("🚀 Enhanced Bitcoin Pattern Scanner")
    print("ماسح البيتكوين المحسن القائم على الأنماط")
    print("=" * 60)
    
    options = {
        "1": ("Interactive Pattern Selection", interactive_pattern_selection),
        "2": ("Demo All Patterns", demo_all_patterns),
        "3": ("Quick Test All Patterns", quick_test_all_patterns),
        "4": ("Run Beginner Strategy", lambda: run_recommended_strategy("beginner")),
        "5": ("Run Intermediate Strategy", lambda: run_recommended_strategy("intermediate")),
        "6": ("Run Advanced Strategy", lambda: run_recommended_strategy("advanced")),
        "7": ("Exit", lambda: print("Goodbye!"))
    }
    
    for key, (desc, _) in options.items():
        print(f"{key}. {desc}")
    
    while True:
        try:
            choice = input(f"\nSelect option (1-{len(options)}): ").strip()
            
            if choice in options:
                if choice == "7":
                    options[choice][1]()
                    break
                else:
                    options[choice][1]()
                    
                    # Ask if user wants to continue
                    cont = input("\nDo you want to run another option? (y/n): ").strip().lower()
                    if cont not in ['y', 'yes', 'نعم']:
                        break
            else:
                print("Invalid choice. Please try again.")
                
        except KeyboardInterrupt:
            print("\nExiting...")
            break
        except Exception as e:
            print(f"Error: {e}")

if __name__ == "__main__":
    main()
