#!/usr/bin/env python3
"""
Bitcoin Key Scanner
A script that generates random Bitcoin private keys within a specified range,
converts them to public addresses, and checks for balances using blockchain APIs.
"""

import hashlib
import base58
import ecdsa
import requests
import time
import json
import random
import threading
from datetime import datetime, timedelta
import argparse
import sys

class BitcoinKeyScanner:
    def __init__(self, start_range=None, end_range=None, duration_minutes=60, output_file="found_keys.txt"):
        """
        Initialize the Bitcoin key scanner
        
        Args:
            start_range: Starting range for private key generation (hex string)
            end_range: Ending range for private key generation (hex string)
            duration_minutes: How long to run the scanner (in minutes)
            output_file: File to save found keys with balances
        """
        self.start_range = int(start_range, 16) if start_range else 1
        self.end_range = int(end_range, 16) if end_range else 2**256 - 1
        self.duration = timedelta(minutes=duration_minutes)
        self.output_file = output_file
        self.start_time = datetime.now()
        self.keys_checked = 0
        self.found_keys = 0
        self.running = True
        
        # Rate limiting for API calls
        self.last_api_call = 0
        self.api_delay = 0.1  # 100ms delay between API calls
        
        print(f"Scanner initialized:")
        print(f"Range: {hex(self.start_range)} to {hex(self.end_range)}")
        print(f"Duration: {duration_minutes} minutes")
        print(f"Output file: {output_file}")
        print("-" * 50)

    def generate_private_key(self):
        """Generate a random private key within the specified range"""
        return random.randint(self.start_range, self.end_range)

    def private_key_to_wif(self, private_key_int):
        """Convert private key integer to Wallet Import Format (WIF)"""
        # Add version byte (0x80 for mainnet)
        extended_key = b'\x80' + private_key_int.to_bytes(32, 'big')
        
        # Add compression flag (0x01 for compressed)
        extended_key += b'\x01'
        
        # Calculate checksum
        checksum = hashlib.sha256(hashlib.sha256(extended_key).digest()).digest()[:4]
        
        # Encode in base58
        return base58.b58encode(extended_key + checksum).decode()

    def private_key_to_address(self, private_key_int):
        """Convert private key to Bitcoin address"""
        try:
            # Create private key object
            private_key_bytes = private_key_int.to_bytes(32, 'big')
            signing_key = ecdsa.SigningKey.from_string(private_key_bytes, curve=ecdsa.SECP256k1)
            
            # Get public key
            verifying_key = signing_key.get_verifying_key()
            public_key_bytes = b'\x02' + verifying_key.to_string()[:32]  # Compressed public key
            
            # Hash public key
            sha256_hash = hashlib.sha256(public_key_bytes).digest()
            ripemd160_hash = hashlib.new('ripemd160', sha256_hash).digest()
            
            # Add version byte (0x00 for mainnet)
            versioned_hash = b'\x00' + ripemd160_hash
            
            # Calculate checksum
            checksum = hashlib.sha256(hashlib.sha256(versioned_hash).digest()).digest()[:4]
            
            # Encode address
            address = base58.b58encode(versioned_hash + checksum).decode()
            
            return address
            
        except Exception as e:
            print(f"Error generating address: {e}")
            return None

    def check_balance(self, address):
        """Check balance of a Bitcoin address using blockchain.info API"""
        try:
            # Rate limiting
            current_time = time.time()
            if current_time - self.last_api_call < self.api_delay:
                time.sleep(self.api_delay - (current_time - self.last_api_call))
            
            self.last_api_call = time.time()
            
            # Make API request
            url = f"https://blockchain.info/q/addressbalance/{address}"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                balance_satoshi = int(response.text)
                balance_btc = balance_satoshi / 100000000  # Convert satoshi to BTC
                return balance_btc
            else:
                return None
                
        except Exception as e:
            print(f"Error checking balance for {address}: {e}")
            return None

    def save_found_key(self, private_key_int, address, balance):
        """Save found key with balance to file"""
        try:
            wif = self.private_key_to_wif(private_key_int)
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            with open(self.output_file, 'a') as f:
                f.write(f"Timestamp: {timestamp}\n")
                f.write(f"Private Key (Hex): {hex(private_key_int)}\n")
                f.write(f"Private Key (WIF): {wif}\n")
                f.write(f"Address: {address}\n")
                f.write(f"Balance: {balance} BTC\n")
                f.write("-" * 50 + "\n")
            
            print(f"🎉 FOUND KEY WITH BALANCE! 🎉")
            print(f"Address: {address}")
            print(f"Balance: {balance} BTC")
            print(f"Saved to: {self.output_file}")
            
        except Exception as e:
            print(f"Error saving key: {e}")

    def print_stats(self):
        """Print current statistics"""
        elapsed = datetime.now() - self.start_time
        remaining = self.duration - elapsed
        
        if remaining.total_seconds() > 0:
            keys_per_second = self.keys_checked / elapsed.total_seconds() if elapsed.total_seconds() > 0 else 0
            
            print(f"\rKeys checked: {self.keys_checked:,} | "
                  f"Found: {self.found_keys} | "
                  f"Rate: {keys_per_second:.2f} keys/sec | "
                  f"Time remaining: {str(remaining).split('.')[0]}", end="")
        else:
            self.running = False

    def run(self):
        """Main scanning loop"""
        print("Starting Bitcoin key scanner...")
        print("Press Ctrl+C to stop early\n")
        
        try:
            while self.running and datetime.now() - self.start_time < self.duration:
                # Generate random private key
                private_key = self.generate_private_key()
                
                # Convert to address
                address = self.private_key_to_address(private_key)
                if not address:
                    continue
                
                # Check balance
                balance = self.check_balance(address)
                if balance is not None and balance > 0:
                    self.found_keys += 1
                    self.save_found_key(private_key, address, balance)
                
                self.keys_checked += 1
                
                # Print stats every 10 keys
                if self.keys_checked % 10 == 0:
                    self.print_stats()
                    
        except KeyboardInterrupt:
            print("\n\nStopped by user")
        except Exception as e:
            print(f"\nError during scanning: {e}")
        
        print(f"\n\nScan completed!")
        print(f"Total keys checked: {self.keys_checked:,}")
        print(f"Keys with balance found: {self.found_keys}")
        print(f"Results saved to: {self.output_file}")


def main():
    parser = argparse.ArgumentParser(description="Bitcoin Key Scanner")
    parser.add_argument("--start", type=str, default="1", 
                       help="Starting range for private key (hex)")
    parser.add_argument("--end", type=str, default="FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364140",
                       help="Ending range for private key (hex)")
    parser.add_argument("--duration", type=float, default=60,
                       help="Duration to run scanner (minutes)")
    parser.add_argument("--output", type=str, default="found_keys.txt",
                       help="Output file for found keys")
    
    args = parser.parse_args()
    
    # Validate hex ranges
    try:
        int(args.start, 16)
        int(args.end, 16)
    except ValueError:
        print("Error: Start and end ranges must be valid hexadecimal values")
        sys.exit(1)
    
    # Create and run scanner
    scanner = BitcoinKeyScanner(
        start_range=args.start,
        end_range=args.end,
        duration_minutes=args.duration,
        output_file=args.output
    )
    
    scanner.run()


if __name__ == "__main__":
    main()
