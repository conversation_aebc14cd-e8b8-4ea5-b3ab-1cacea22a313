#!/usr/bin/env python3
"""
أمثلة على استخدام Bitcoin Key Scanner
Examples for using Bitcoin Key Scanner
"""

from bitcoin_key_scanner import BitcoinKeyScanner
import time

def example_small_range():
    """مثال: البحث في نطاق صغير لمدة دقيقة واحدة"""
    print("=== مثال 1: نطاق صغير ===")
    scanner = BitcoinKeyScanner(
        start_range="1",
        end_range="FFFF",  # نطاق صغير للاختبار
        duration_minutes=1,
        output_file="small_range_results.txt"
    )
    scanner.run()
    print("\n")

def example_medium_range():
    """مثال: البحث في نطاق متوسط لمدة 5 دقائق"""
    print("=== مثال 2: نطاق متوسط ===")
    scanner = BitcoinKeyScanner(
        start_range="10000",
        end_range="FFFFFFFF",
        duration_minutes=5,
        output_file="medium_range_results.txt"
    )
    scanner.run()
    print("\n")

def example_custom_range():
    """مثال: نطاق مخصص"""
    print("=== مثال 3: نطاق مخصص ===")
    
    # يمكنك تحديد أي نطاق تريده
    start = "1000000000000000"  # 16 خانة hex
    end = "1FFFFFFFFFFFFFF"     # نطاق محدد
    
    scanner = BitcoinKeyScanner(
        start_range=start,
        end_range=end,
        duration_minutes=2,
        output_file="custom_range_results.txt"
    )
    scanner.run()
    print("\n")

def test_single_key():
    """اختبار توليد مفتاح واحد وفحصه"""
    print("=== اختبار مفتاح واحد ===")
    
    scanner = BitcoinKeyScanner(
        start_range="1",
        end_range="100",
        duration_minutes=1,
        output_file="test_results.txt"
    )
    
    # توليد مفتاح واحد للاختبار
    private_key = scanner.generate_private_key()
    address = scanner.private_key_to_address(private_key)
    
    print(f"Private Key: {hex(private_key)}")
    print(f"Address: {address}")
    
    if address:
        balance = scanner.check_balance(address)
        print(f"Balance: {balance} BTC" if balance is not None else "Could not check balance")
    
    print("\n")

def main():
    """تشغيل جميع الأمثلة"""
    print("Bitcoin Key Scanner - أمثلة الاستخدام")
    print("=" * 50)
    
    # اختبار مفتاح واحد أولاً
    test_single_key()
    
    # تشغيل الأمثلة
    try:
        example_small_range()
        time.sleep(2)  # انتظار قصير بين الأمثلة
        
        example_medium_range()
        time.sleep(2)
        
        example_custom_range()
        
    except KeyboardInterrupt:
        print("\nتم إيقاف الأمثلة بواسطة المستخدم")
    
    print("انتهت جميع الأمثلة!")

if __name__ == "__main__":
    main()
