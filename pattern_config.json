{"patterns": {"sequential": {"description": "Sequential keys in early Bitcoin ranges", "description_ar": "مفاتيح متسلسلة في نطاقات البيتكوين المبكرة", "probability": "Medium", "ranges": [{"start": "0x1", "end": "0xF4240", "description": "Very early keys (1-1M)"}, {"start": "0x1000", "end": "0x100000", "description": "Early hex range"}, {"start": "0x123456", "end": "0x1234567", "description": "Sequential patterns"}]}, "vanity_1": {"description": "Keys likely to produce addresses starting with '1A', '1B', etc.", "description_ar": "مفاتيح محتملة لإنتاج عناوين تبدأ بـ '1A', '1B', إلخ", "probability": "High", "target_prefixes": ["1A", "1B", "1C", "1D", "1E"], "ranges": [{"start": "0x1A000000", "end": "0x1AFFFFFF"}, {"start": "0x1B000000", "end": "0x1BFFFFFF"}, {"start": "0x1C000000", "end": "0x1CFFFFFF"}]}, "vanity_3": {"description": "Keys for P2SH addresses (starting with '3')", "description_ar": "مفاتيح لعناوين P2SH (تبدأ بـ '3')", "probability": "Medium", "target_prefixes": ["3A", "3B", "3C"], "ranges": [{"start": "0x3A000000", "end": "0x3AFFFFFF"}, {"start": "0x3B000000", "end": "0x3BFFFFFF"}, {"start": "0x30000000", "end": "0x3FFFFFFF"}]}, "repeated_digits": {"description": "Keys with repeated digits or patterns", "description_ar": "مفاتيح بأرقام أو أنماط متكررة", "probability": "Medium", "patterns": ["0x1111111111111111", "0x2222222222222222", "0x1234567890ABCDEF", "0xAAAAAAAAAAAAAAAA", "0x1010101010101010", "0x123123123123123", "0xABCABCABCABCABC"]}, "low_entropy": {"description": "Keys with low entropy (common in early Bitcoin)", "description_ar": "مفاتيح بإنتروبيا منخفضة (شائعة في البيتكوين المبكر)", "probability": "High", "entropy_bits": [8, 16, 24, 32, 40, 48], "description_note": "Early Bitcoin users often used simple passwords"}, "birthday_dates": {"description": "Keys based on common birthday dates", "description_ar": "مفاتي<PERSON> مبنية على تواريخ ميلاد شائعة", "probability": "Medium", "date_ranges": {"years": [1970, 2020], "months": [1, 12], "days": [1, 28]}, "formats": ["YYYYMMDD", "DDMMYYYY", "MMDDYYYY"]}, "common_numbers": {"description": "Keys based on common numbers and sequences", "description_ar": "مفاتي<PERSON> مبنية على أرقام وتسلسلات شائعة", "probability": "High", "numbers": [123456789, 987654321, 111111111, 999999999, 1234567890, 1111111111, 2222222222, 12345, 54321, 11111, 99999, 77777, 1337, 2020, 2021, 2022, 2023, 2024, 42, 69, 420, 666, 777, 888, 999]}, "hex_patterns": {"description": "Keys with specific hex patterns", "description_ar": "مفاتيح بأنماط hex محددة", "probability": "Medium", "patterns": ["DEADBEEF", "CAFEBABE", "FEEDFACE", "BADCAFE", "12345678", "87654321", "ABCDEFAB", "FEDCBA98", "11111111", "AAAAAAAA", "FFFFFFFF", "00000000"]}}, "optimization_strategies": {"batch_processing": {"description": "Process keys in batches for better performance", "recommended_batch_size": 1000, "max_batch_size": 5000}, "threading": {"description": "Use multiple threads for concurrent processing", "recommended_workers": 5, "max_workers": 10}, "api_optimization": {"description": "Optimize API calls to avoid rate limiting", "delay_between_calls": 0.05, "timeout": 10, "retry_attempts": 3}}, "target_address_patterns": {"high_probability": ["Addresses starting with 1A-1E", "Addresses with repeated characters", "Addresses from early Bitcoin era", "Addresses with low entropy patterns"], "medium_probability": ["P2SH addresses (starting with 3)", "Addresses with date patterns", "Addresses with common number sequences"], "research_notes": {"early_bitcoin": "Many early Bitcoin users used simple passwords or patterns", "vanity_addresses": "Some users created vanity addresses with specific patterns", "brain_wallets": "Brain wallets often used predictable phrases or numbers", "weak_randomness": "Early software sometimes had weak random number generation"}}, "recommended_scanning_strategies": {"beginner": {"pattern": "low_entropy", "batch_size": 500, "duration": 30, "workers": 3}, "intermediate": {"pattern": "common_numbers", "batch_size": 1000, "duration": 60, "workers": 5}, "advanced": {"pattern": "sequential", "batch_size": 2000, "duration": 120, "workers": 8}, "research": {"pattern": "vanity_1", "batch_size": 1500, "duration": 180, "workers": 10}}}