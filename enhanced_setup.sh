#!/bin/bash

# Enhanced Bitcoin Pattern Scanner Setup Script
# سكربت إعداد ماسح البيتكوين المحسن القائم على الأنماط

echo "=================================================================="
echo "🚀 Enhanced Bitcoin Pattern Scanner - Setup Script"
echo "سكربت إعداد ماسح البيتكوين المحسن القائم على الأنماط"
echo "=================================================================="
echo

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠️${NC} $1"
}

print_error() {
    echo -e "${RED}❌${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ️${NC} $1"
}

# Check if Python 3 is installed
echo "🔍 Checking system requirements..."
if command -v python3 &> /dev/null; then
    PYTHON_VERSION=$(python3 --version)
    print_status "Python found: $PYTHON_VERSION"
else
    print_error "Python 3 is not installed. Please install Python 3.6 or higher."
    exit 1
fi

# Check if pip is installed
if command -v pip3 &> /dev/null; then
    print_status "pip3 found"
    PIP_CMD="pip3"
elif command -v pip &> /dev/null; then
    print_status "pip found"
    PIP_CMD="pip"
else
    print_error "pip is not installed. Please install pip."
    exit 1
fi

# Install requirements
echo
echo "📦 Installing Python dependencies..."
echo "تثبيت المتطلبات..."

if $PIP_CMD install -r requirements.txt; then
    print_status "Dependencies installed successfully"
    print_status "تم تثبيت المتطلبات بنجاح"
else
    print_error "Failed to install dependencies"
    print_error "فشل في تثبيت المتطلبات"
    exit 1
fi

# Make scripts executable
echo
echo "🔧 Making scripts executable..."
chmod +x enhanced_bitcoin_scanner.py
chmod +x pattern_examples.py
chmod +x test_patterns.py
chmod +x bitcoin_key_scanner.py
chmod +x example_usage.py
chmod +x test_scanner.py

print_status "Scripts made executable"

# Create logs directory if it doesn't exist
if [ ! -d "logs" ]; then
    mkdir logs
    print_status "Created logs directory"
fi

# Run comprehensive tests
echo
echo "🧪 Running comprehensive system tests..."
echo "تشغيل اختبارات النظام الشاملة..."

if python3 test_patterns.py; then
    print_status "System tests completed successfully"
    print_status "اختبارات النظام مكتملة بنجاح"
else
    print_warning "Some tests may have failed, but the scanner should still work"
    print_warning "بعض الاختبارات قد فشلت، لكن الماسح يجب أن يعمل"
fi

echo
echo "=================================================================="
print_status "Enhanced Setup completed! الإعداد المحسن مكتمل!"
echo "=================================================================="
echo

# Display usage examples
echo -e "${BLUE}📖 Usage Examples / أمثلة الاستخدام:${NC}"
echo
echo "1. 🎮 Interactive Pattern Selection (تفاعلي):"
echo "   python3 pattern_examples.py"
echo
echo "2. 🔍 Quick Pattern Test (اختبار سريع):"
echo "   python3 enhanced_bitcoin_scanner.py --pattern low_entropy --duration 5"
echo
echo "3. ⚡ High Performance Scan (فحص عالي الأداء):"
echo "   python3 enhanced_bitcoin_scanner.py --pattern common_numbers --batch-size 2000 --workers 8"
echo
echo "4. 🎯 Vanity Address Search (البحث عن عناوين مميزة):"
echo "   python3 enhanced_bitcoin_scanner.py --pattern vanity_1 --duration 30"
echo
echo "5. 📊 View Help (عرض المساعدة):"
echo "   python3 enhanced_bitcoin_scanner.py --help"
echo

# Display available patterns
echo -e "${BLUE}🔍 Available Patterns / الأنماط المتاحة:${NC}"
echo
echo "High Probability (احتمالية عالية):"
echo "  • low_entropy      - Low entropy keys (مفاتيح بإنتروبيا منخفضة)"
echo "  • common_numbers   - Common number sequences (أرقام شائعة)"
echo "  • vanity_1         - Vanity addresses starting with 1 (عناوين مميزة تبدأ بـ 1)"
echo
echo "Medium Probability (احتمالية متوسطة):"
echo "  • sequential       - Sequential key ranges (نطاقات متسلسلة)"
echo "  • repeated_digits  - Repeated digit patterns (أنماط أرقام متكررة)"
echo "  • birthday_dates   - Birthday date patterns (أنماط تواريخ الميلاد)"
echo "  • hex_patterns     - Hex pattern keys (أنماط hex)"
echo "  • vanity_3         - P2SH addresses starting with 3 (عناوين P2SH تبدأ بـ 3)"
echo

# Display recommended strategies
echo -e "${BLUE}🎯 Recommended Strategies / الاستراتيجيات الموصى بها:${NC}"
echo
echo "Beginner (مبتدئ):"
echo "  python3 enhanced_bitcoin_scanner.py --pattern low_entropy --batch-size 500 --duration 30"
echo
echo "Intermediate (متوسط):"
echo "  python3 enhanced_bitcoin_scanner.py --pattern common_numbers --batch-size 1000 --duration 60"
echo
echo "Advanced (متقدم):"
echo "  python3 enhanced_bitcoin_scanner.py --pattern sequential --batch-size 2000 --duration 120 --workers 8"
echo

# Display important notes
echo -e "${YELLOW}⚠️ Important Notes / ملاحظات مهمة:${NC}"
echo
echo "• This tool is for educational and research purposes only"
echo "  هذه الأداة للأغراض التعليمية والبحثية فقط"
echo
echo "• The probability of finding keys with balance is extremely low"
echo "  احتمالية العثور على مفاتيح تحتوي على رصيد منخفضة جداً"
echo
echo "• Respect your local laws and regulations"
echo "  احترم القوانين واللوائح المحلية"
echo
echo "• Monitor resource usage (CPU, memory, network)"
echo "  راقب استهلاك الموارد (المعالج، الذاكرة، الشبكة)"
echo
echo "• Start with short durations for testing"
echo "  ابدأ بفترات قصيرة للاختبار"
echo

# Display file structure
echo -e "${BLUE}📁 Project Structure / هيكل المشروع:${NC}"
echo
echo "Enhanced Scanner Files:"
echo "  • enhanced_bitcoin_scanner.py  - Main enhanced scanner"
echo "  • pattern_examples.py          - Interactive examples"
echo "  • test_patterns.py             - Comprehensive tests"
echo "  • pattern_config.json          - Pattern configurations"
echo "  • ENHANCED_README.md           - Detailed documentation"
echo
echo "Original Scanner Files:"
echo "  • bitcoin_key_scanner.py       - Original scanner"
echo "  • example_usage.py             - Basic examples"
echo "  • test_scanner.py              - Basic tests"
echo
echo "Configuration & Documentation:"
echo "  • requirements.txt             - Python dependencies"
echo "  • config.json                  - Basic configuration"
echo "  • README.md                    - Basic documentation"
echo "  • logs/                        - Log files directory"
echo

echo "=================================================================="
print_status "Setup complete! Ready to scan for Bitcoin keys!"
print_status "الإعداد مكتمل! جاهز لفحص مفاتيح البيتكوين!"
echo "=================================================================="
