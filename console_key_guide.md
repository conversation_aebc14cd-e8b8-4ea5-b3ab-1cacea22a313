# 🖥️ Console Key Viewer Guide
# دليل عارض المفاتيح في الكونسول

## 📋 نظرة عامة

تم إنشاء **ثلاثة سكريبتات مختلفة** لعرض المفاتيح ومحتواها في الكونسول:

### 1. 🔍 **Console Key Viewer** (عرض مفصل)
```bash
python3 console_key_viewer.py
```
- **الوصف**: عرض شامل ومفصل لكل مفتاح
- **المميزات**: تحليل كامل، معلومات تقنية، فحص رصيد
- **الاستخدام**: للتحليل العميق والتعلم

### 2. ⚡ **Quick Key Display** (عرض سريع)
```bash
python3 quick_key_display.py
```
- **الوصف**: عرض سريع ومبسط للمفاتيح
- **المميزات**: 3 أنماط عرض (simple, compact, table)
- **الاستخدام**: للفحص السريع والمقارنة

### 3. 🚀 **Enhanced Scanner** (الماسح المحسن)
```bash
python3 enhanced_bitcoin_scanner.py
```
- **الوصف**: فحص متقدم بالدفعات مع logging
- **المميزات**: 8 أنماط، معالجة متوازية، حفظ النتائج
- **الاستخدام**: للبحث الجدي والمكثف

---

## 🎯 أمثلة الاستخدام

### العرض المفصل (Console Key Viewer)
```bash
# تشغيل الواجهة التفاعلية
python3 console_key_viewer.py

# الخيارات المتاحة:
# 1. Generate Low Entropy Keys (مفاتيح بإنتروبيا منخفضة)
# 2. Generate Common Number Keys (مفاتيح أرقام شائعة)  
# 3. Generate Sequential Keys (مفاتيح متسلسلة)
# 4. Generate Vanity Keys (مفاتيح مميزة)
# 5. Generate Random Pattern (نمط عشوائي)
# 6. Custom Pattern (نمط مخصص)
```

**مثال على الإخراج:**
```
================================================================================
🔑 KEY #001 - معلومات المفتاح رقم 1
================================================================================
📊 BASIC INFORMATION - المعلومات الأساسية
--------------------------------------------------
🔢 Private Key (Decimal): 32,688
🔤 Private Key (Hex): 0x7fb0
📝 Private Key (Binary): 0b111111110110000
💼 WIF Format: KwDiBf89QgGbjEhKnhXJuH7LrciVrZi3qYjgd9M7rFYwh3PsUiwK
🏠 Bitcoin Address: **********************************

🔍 KEY ANALYSIS - تحليل المفتاح
--------------------------------------------------
📏 Hex Length: 4 characters
🎲 Entropy Level: Very Low (منخفض جداً)
🔍 Pattern Analysis: No obvious patterns

🏠 ADDRESS ANALYSIS - تحليل العنوان
--------------------------------------------------
🏷️ Address Type: P2PKH (Legacy) - عنوان تقليدي
🔤 Address Prefix: 19

💰 BALANCE CHECK - فحص الرصيد
--------------------------------------------------
🔍 Checking balance... فحص الرصيد...
💸 No balance (0 BTC)

⚙️ TECHNICAL DETAILS - التفاصيل التقنية
--------------------------------------------------
🔑 Public Key (Compressed): 020da41f3c5d814672808e6a9baccf7e210de5ff...
🔐 SHA256 Hash: 4d80c9394508a212c067ee9c5624b7776c4eec16da39fac152cb4b91e7a3ec2e
⏰ Generated at: 2025-08-17 17:10:38
```

### العرض السريع (Quick Key Display)

#### 1. العرض البسيط (Simple)
```bash
python3 quick_key_display.py --pattern low_entropy --count 3 --format simple
```

**الإخراج:**
```
============================================================
🔑 KEY #001 | المفتاح رقم 1
============================================================
🔢 Private Key (Dec): 32,688
🔤 Private Key (Hex): 0x7fb0
💼 WIF: KwDiBf89QgGbjEhKnhXJuH7LrciVrZi3qYjgd9M7rFYwh3PsUiwK
🏠 Address: **********************************
🎲 Entropy: Very Low (4 hex chars)
🏷️ Type: Legacy (P2PKH)
💰 Checking balance...
💸 No balance (0 BTC)
⏰ Generated: 17:10:38
```

#### 2. العرض المضغوط (Compact)
```bash
python3 quick_key_display.py --pattern common_numbers --count 5 --format compact
```

**الإخراج:**
```
#001 |             0x7df7f3 | ********************************** | 💸 0 BTC
#002 |                 0x5f | ********************************** | 💸 0 BTC
#003 |         0x61fb86ed10 | ********************************** | 💸 0 BTC
#004 |               0x2a42 | ********************************** | 💸 0 BTC
#005 |        0x2dfdc1c3400 | ********************************** | 💸 0 BTC
```

#### 3. عرض الجدول (Table)
```bash
python3 quick_key_display.py --pattern sequential --count 5 --format table
```

**الإخراج:**
```
| #   |  Private Key (Hex) | Bitcoin Address                    |      Balance |
|-----|--------------------|------------------------------------|--------------|
|   1 |               0x2a | ********************************** |        0 BTC |
|   2 |        0x2dfdc1c34 | ********************************** |        0 BTC |
|   3 |        0x2540a5d60 | ********************************** |        0 BTC |
|   4 |       0x19debd01bc | ********************************** |        0 BTC |
|   5 |        0x2dfdc1c34 | ********************************** |        0 BTC |
```

### العرض بدون فحص الرصيد (أسرع)
```bash
python3 quick_key_display.py --pattern vanity_1 --count 10 --format compact --no-balance-check
```

**الإخراج:**
```
#001 |         0x1a2b3c4d | **********************************
#002 |         0x1b4e5f6a | 1B2cD3eF4gH5iJ6kL7mN8oP9qR0sT1uV2w
#003 |         0x1c7f8a9b | 1C3dE4fG5hI6jK7lM8nO9pQ0rS1tU2vW3x
```

---

## 🎮 الواجهة التفاعلية

### Quick Key Display - Interactive Mode
```bash
python3 quick_key_display.py
```

**القائمة التفاعلية:**
```
🎮 Quick Key Display - Interactive Mode
عرض سريع للمفاتيح - الوضع التفاعلي
==================================================

Options:
1. Generate 5 low entropy keys
2. Generate 10 common number keys
3. Generate 3 vanity keys
4. Custom generation
5. Table view (10 keys)
6. Compact view (20 keys)
7. Exit

Select (1-7):
```

---

## 📊 مقارنة أنماط العرض

| النمط | السرعة | التفاصيل | الاستخدام المناسب |
|-------|---------|-----------|-------------------|
| **Console Viewer** | بطيء | شامل جداً | التعلم والتحليل العميق |
| **Quick Simple** | متوسط | متوازن | الفحص العام |
| **Quick Compact** | سريع | أساسي | المقارنة السريعة |
| **Quick Table** | سريع | منظم | عرض متعدد المفاتيح |
| **No Balance Check** | سريع جداً | بدون رصيد | التوليد السريع |

---

## 🔧 خيارات سطر الأوامر

### Quick Key Display Options
```bash
python3 quick_key_display.py [OPTIONS]

--pattern PATTERN     # نوع النمط (low_entropy, common_numbers, etc.)
--count COUNT         # عدد المفاتيح (1-50)
--format FORMAT       # نمط العرض (simple, compact, table)
--no-balance-check    # تخطي فحص الرصيد للسرعة
```

### أمثلة متقدمة
```bash
# عرض 20 مفتاح بنمط hex في جدول بدون فحص رصيد
python3 quick_key_display.py \
    --pattern hex_patterns \
    --count 20 \
    --format table \
    --no-balance-check

# عرض 3 مفاتيح vanity بتفاصيل كاملة
python3 quick_key_display.py \
    --pattern vanity_1 \
    --count 3 \
    --format simple

# عرض مضغوط لـ 50 مفتاح sequential
python3 quick_key_display.py \
    --pattern sequential \
    --count 50 \
    --format compact
```

---

## 🎯 نصائح للاستخدام الأمثل

### 1. للتعلم والفهم
```bash
# استخدم العرض المفصل مع عدد قليل
python3 console_key_viewer.py
# اختر 1-3 مفاتيح لفهم التفاصيل
```

### 2. للفحص السريع
```bash
# استخدم العرض المضغوط مع عدد كبير
python3 quick_key_display.py --count 20 --format compact
```

### 3. للمقارنة
```bash
# استخدم عرض الجدول
python3 quick_key_display.py --count 10 --format table
```

### 4. للسرعة القصوى
```bash
# تخطي فحص الرصيد
python3 quick_key_display.py --count 50 --no-balance-check
```

---

## 🔍 فهم المخرجات

### رموز الحالة
- 🔑 **مفتاح جديد**
- 💰 **رصيد موجود** 
- 💸 **لا يوجد رصيد**
- ❌ **خطأ في الفحص**
- 🎉 **تم العثور على رصيد!**

### مستويات الإنتروبيا
- **Very Low**: أقل من 8 أحرف hex
- **Low**: 8-16 حرف hex
- **Medium**: 16-32 حرف hex
- **High**: 32+ حرف hex

### أنواع العناوين
- **P2PKH (Legacy)**: يبدأ بـ 1
- **P2SH (Script)**: يبدأ بـ 3
- **Bech32 (SegWit)**: يبدأ بـ bc1

---

## ⚠️ ملاحظات مهمة

### الأداء
- **العرض المفصل**: 1-2 مفتاح/ثانية
- **العرض السريع**: 5-10 مفتاح/ثانية
- **بدون فحص رصيد**: 50+ مفتاح/ثانية

### استهلاك الموارد
- **الذاكرة**: 50-150 MB
- **الشبكة**: 1KB لكل فحص رصيد
- **المعالج**: منخفض إلى متوسط

### الأمان
- جميع السكريبتات آمنة للاستخدام
- لا يتم حفظ المفاتيح تلقائياً
- فحص الرصيد عبر API عامة

---

## 🎉 الخلاصة

تم توفير **ثلاثة خيارات مختلفة** لعرض المفاتيح في الكونسول:

1. **📖 للتعلم**: `console_key_viewer.py` - عرض شامل ومفصل
2. **⚡ للسرعة**: `quick_key_display.py` - عرض سريع ومرن
3. **🚀 للبحث**: `enhanced_bitcoin_scanner.py` - فحص متقدم

**اختر الأداة المناسبة حسب احتياجاتك!**
