2025-08-17 17:04:04,211 - INFO - <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> initialized:
2025-08-17 17:04:04,213 - INFO - Pattern: low_entropy
2025-08-17 17:04:04,213 - INFO - Batch size: 1000
2025-08-17 17:04:04,214 - INFO - Duration: 60.0 minutes
2025-08-17 17:04:04,214 - INFO - Output file: found_keys_enhanced.txt
2025-08-17 17:04:04,214 - INFO - --------------------------------------------------
2025-08-17 17:04:04,214 - INFO - Starting enhanced Bitcoin key scanner with pattern-based optimization...
2025-08-17 17:04:04,214 - INFO - Using pattern: low_entropy
2025-08-17 17:04:04,214 - INFO - Press Ctrl+C to stop early

2025-08-17 17:04:04,214 - INFO - Processing batch 1 (1000 keys)...
2025-08-17 17:05:11,276 - INFO - Batch 1 completed. Keys checked so far: 1,000
2025-08-17 17:05:12,281 - INFO - Processing batch 2 (1000 keys)...
2025-08-17 17:06:17,907 - INFO - Batch 2 completed. Keys checked so far: 2,000
2025-08-17 17:06:18,910 - INFO - Processing batch 3 (1000 keys)...
2025-08-17 17:07:09,378 - INFO - 
Stopped by user
