# 🚀 Bitcoin Key Scanner Project
# مشروع ماسح مفاتيح البيتكوين

## 📋 نظرة عامة

مشروع شامل لفحص مفاتيح البيتكوين يتضمن **نسختين مكملتين**:

### 🎯 النسخة المحسنة (موصى بها)
**Enhanced Bitcoin Pattern Scanner** - سكربت متقدم قائم على الأنماط المحتملة

### 📊 النسخة الأساسية
**Basic Bitcoin Key Scanner** - سكربت تقليدي للفحص العام

---

## 🚀 البدء السريع

### للمستخدمين الجدد (موصى به)
```bash
# تشغيل الإعداد المحسن
./enhanced_setup.sh

# تشغيل الواجهة التفاعلية
python3 pattern_examples.py
```

### للمستخدمين المتقدمين
```bash
# النسخة المحسنة - فحص بنمط محدد
python3 enhanced_bitcoin_scanner.py --pattern low_entropy --duration 30

# النسخة الأساسية - فحص تقليدي
python3 bitcoin_key_scanner.py --duration 30
```

---

## 📁 اختيار النسخة المناسبة

| المعيار | النسخة الأساسية | النسخة المحسنة |
|---------|-----------------|-----------------|
| **سهولة الاستخدام** | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| **الأداء** | ⭐⭐ | ⭐⭐⭐⭐⭐ |
| **الأنماط** | عشوائي فقط | 8 أنماط محسنة |
| **الاحتمالية** | منخفضة | محسنة |
| **التحكم** | أساسي | متقدم |

### 🎯 استخدم النسخة المحسنة إذا كنت تريد:
- ✅ أفضل فرص للعثور على مفاتيح تحتوي على رصيد
- ✅ أنماط محسنة مبنية على البحث العلمي
- ✅ أداء عالي مع معالجة الدفعات
- ✅ واجهة تفاعلية سهلة الاستخدام
- ✅ نظام logging متقدم

### 📊 استخدم النسخة الأساسية إذا كنت تريد:
- ✅ فحص بسيط وسريع
- ✅ استهلاك موارد أقل
- ✅ فهم المبادئ الأساسية
- ✅ تخصيص بسيط

---

## 📖 الأدلة والتوثيق

### للنسخة المحسنة
- 📚 **[ENHANCED_README.md](ENHANCED_README.md)** - دليل شامل
- 📋 **[ENHANCED_SUMMARY.md](ENHANCED_SUMMARY.md)** - ملخص مفصل
- ⚙️ **[pattern_config.json](pattern_config.json)** - تكوين الأنماط

### للنسخة الأساسية
- 📚 **[README.md](README.md)** - هذا الملف (الدليل الأساسي)
- 📋 **[SUMMARY.md](SUMMARY.md)** - ملخص أساسي
- ⚙️ **[config.json](config.json)** - تكوين أساسي

### التوثيق العام
- 🎯 **[FINAL_PROJECT_SUMMARY.md](FINAL_PROJECT_SUMMARY.md)** - ملخص المشروع الكامل

---

## 🔧 الإعداد والتثبيت

### الإعداد التلقائي (موصى به)
```bash
# للنسخة المحسنة
./enhanced_setup.sh

# للنسخة الأساسية
./setup.sh
```

### الإعداد اليدوي
```bash
# تثبيت المتطلبات
pip3 install -r requirements.txt

# جعل الملفات قابلة للتنفيذ
chmod +x *.py *.sh

# تشغيل الاختبارات
python3 test_patterns.py    # للنسخة المحسنة
python3 test_scanner.py     # للنسخة الأساسية
```

---

## 🎯 أمثلة سريعة

### النسخة المحسنة (موصى بها)
```bash
# واجهة تفاعلية لاختيار الأنماط
python3 pattern_examples.py

# فحص بنمط عالي الاحتمالية
python3 enhanced_bitcoin_scanner.py --pattern low_entropy --duration 30

# فحص متقدم بأداء عالي
python3 enhanced_bitcoin_scanner.py \
    --pattern common_numbers \
    --batch-size 2000 \
    --workers 8 \
    --duration 60
```

### النسخة الأساسية
```bash
# فحص أساسي
python3 bitcoin_key_scanner.py

# فحص مخصص
python3 bitcoin_key_scanner.py --start 1 --end FFFF --duration 30
```

---

## 🔍 الأنماط المتاحة (النسخة المحسنة)

| النمط | الاحتمالية | الوصف |
|-------|------------|--------|
| `low_entropy` | ⭐⭐⭐ | مفاتيح بإنتروبيا منخفضة |
| `common_numbers` | ⭐⭐⭐ | أرقام شائعة ومتسلسلات |
| `vanity_1` | ⭐⭐⭐ | عناوين تبدأ بـ 1A, 1B, إلخ |
| `sequential` | ⭐⭐ | نطاقات متسلسلة |
| `repeated_digits` | ⭐⭐ | أنماط أرقام متكررة |
| `birthday_dates` | ⭐⭐ | تواريخ ميلاد شائعة |
| `hex_patterns` | ⭐⭐ | أنماط hex محددة |
| `vanity_3` | ⭐⭐ | عناوين P2SH (تبدأ بـ 3) |

---

## 📊 مقارنة الأداء

| المعيار | النسخة الأساسية | النسخة المحسنة |
|---------|-----------------|-----------------|
| **معدل الفحص** | 3-8 مفتاح/ثانية | 15-50 مفتاح/ثانية |
| **الأنماط** | عشوائي فقط | 8 أنماط محسنة |
| **المعالجة** | تسلسلية | متوازية (دفعات) |
| **Logging** | أساسي | متقدم (4 مستويات) |
| **الواجهة** | سطر أوامر | تفاعلية + سطر أوامر |
| **استهلاك الذاكرة** | 50-100 MB | 150-300 MB |

---

## 🧪 الاختبارات

### اختبار النسخة المحسنة
```bash
python3 test_patterns.py           # جميع الاختبارات
python3 test_patterns.py patterns  # اختبار الأنماط فقط
```

### اختبار النسخة الأساسية
```bash
python3 test_scanner.py            # اختبارات أساسية
```

---

## ⚠️ اعتبارات مهمة

### الأمان والقانونية
- 🎓 **الغرض التعليمي**: هذا المشروع للأغراض التعليمية والبحثية فقط
- 📊 **الاحتمالية**: احتمالية العثور على مفاتيح تحتوي على رصيد منخفضة جداً
- ⚖️ **القوانين المحلية**: احترم قوانين ولوائح بلدك
- 🔒 **الأخلاقيات**: لا تستخدم مفاتيح تخص أشخاص آخرين

### أفضل الممارسات
- 🕐 **ابدأ بفترات قصيرة** للاختبار (5-15 دقيقة)
- 📈 **راقب استهلاك الموارد** (CPU, RAM, Network)
- 💾 **احفظ النتائج بانتظام**
- 🔄 **جرب أنماط مختلفة** لزيادة الفرص
- 📝 **راجع السجلات** لتحسين الأداء

---

## 🆘 الدعم والمساعدة

### استكشاف الأخطاء
```bash
# تشخيص المشاكل
python3 test_patterns.py

# فحص السجلات
cat logs/bitcoin_scanner_*.log

# التحقق من الاتصال
ping blockchain.info
```

### الحصول على المساعدة
- 📖 **للنسخة المحسنة**: اقرأ `ENHANCED_README.md`
- 📊 **للنسخة الأساسية**: اقرأ هذا الملف
- 🎮 **للأمثلة التفاعلية**: شغل `python3 pattern_examples.py`
- 🧪 **للاختبارات**: شغل `python3 test_patterns.py`

---

## 📁 هيكل المشروع

```
📦 Bitcoin Key Scanner Project
├── 🚀 النسخة المحسنة
│   ├── enhanced_bitcoin_scanner.py    # السكربت الرئيسي المحسن
│   ├── pattern_examples.py            # أمثلة تفاعلية
│   ├── test_patterns.py               # اختبارات شاملة
│   ├── pattern_config.json            # تكوين الأنماط
│   ├── ENHANCED_README.md             # دليل شامل
│   └── enhanced_setup.sh              # إعداد محسن
├── 📊 النسخة الأساسية
│   ├── bitcoin_key_scanner.py         # السكربت الأساسي
│   ├── example_usage.py               # أمثلة أساسية
│   ├── test_scanner.py                # اختبارات أساسية
│   ├── config.json                    # تكوين أساسي
│   └── setup.sh                       # إعداد أساسي
└── 🔧 ملفات مشتركة
    ├── requirements.txt               # المتطلبات
    ├── README.md                      # هذا الملف
    ├── FINAL_PROJECT_SUMMARY.md       # ملخص شامل
    └── logs/                          # مجلد السجلات
```

---

## الاستخدام

### الاستخدام الأساسي
```bash
python bitcoin_key_scanner.py
```

### خيارات متقدمة
```bash
python bitcoin_key_scanner.py --start 1 --end FFFFF --duration 30 --output my_keys.txt
```

### المعاملات المتاحة

- `--start`: نقطة البداية للنطاق (hex) - افتراضي: 1
- `--end`: نقطة النهاية للنطاق (hex) - افتراضي: أقصى قيمة صحيحة
- `--duration`: مدة التشغيل بالدقائق - افتراضي: 60
- `--output`: ملف حفظ النتائج - افتراضي: found_keys.txt

## أمثلة

### البحث في نطاق صغير لمدة 10 دقائق
```bash
python bitcoin_key_scanner.py --start 1 --end FFFF --duration 10
```

### البحث في نطاق كبير لمدة ساعة
```bash
python bitcoin_key_scanner.py --start 10000 --end FFFFFFFFFF --duration 60
```

## كيف يعمل السكربت

1. **توليد المفتاح الخاص**: يتم توليد رقم عشوائي ضمن النطاق المحدد
2. **إنشاء العنوان العام**: تحويل المفتاح الخاص إلى عنوان Bitcoin صحيح
3. **فحص الرصيد**: استعلام blockchain.info API للتحقق من الرصيد
4. **حفظ النتائج**: إذا وُجد رصيد، يتم حفظ المفتاح والعنوان والرصيد

## تنسيق ملف النتائج

عند العثور على مفتاح يحتوي على رصيد، يتم حفظه بالتنسيق التالي:

```
Timestamp: 2024-01-15 14:30:25
Private Key (Hex): 0x1a2b3c4d5e6f...
Private Key (WIF): L1234567890abcdef...
Address: **********************************
Balance: 0.00123456 BTC
--------------------------------------------------
```

## الأمان والاعتبارات

⚠️ **تحذيرات مهمة:**

1. **الغرض التعليمي**: هذا السكربت مخصص للأغراض التعليمية والبحثية فقط
2. **الاحتمالية المنخفضة**: احتمالية العثور على مفتاح يحتوي على رصيد منخفضة جداً
3. **استهلاك الموارد**: السكربت يستخدم الإنترنت ومعالج الكمبيوتر
4. **حدود API**: يتم تطبيق تأخير بين الطلبات لتجنب حظر IP

## المتطلبات التقنية

- Python 3.6+
- اتصال إنترنت مستقر
- مساحة تخزين لحفظ النتائج

## استكشاف الأخطاء

### خطأ في الاتصال بالإنترنت
```
Error checking balance for [address]: [error details]
```
**الحل**: تأكد من اتصال الإنترنت وأن blockchain.info متاح

### خطأ في تثبيت المكتبات
```
ModuleNotFoundError: No module named 'ecdsa'
```
**الحل**: قم بتشغيل `pip install -r requirements.txt`

### توقف السكربت
- اضغط `Ctrl+C` لإيقاف السكربت بأمان
- سيتم حفظ الإحصائيات النهائية

## 🎉 الخلاصة

هذا المشروع يوفر **نظاماً شاملاً ومتطوراً** لفحص مفاتيح البيتكوين مع:

### ✅ المميزات المحققة
- 🎯 **نسختان مكملتان**: أساسية ومحسنة لجميع المستويات
- 🔍 **8 أنماط محسنة** مبنية على البحث العلمي
- ⚡ **أداء عالي** مع معالجة الدفعات المتوازية
- 🎮 **واجهة تفاعلية** سهلة الاستخدام
- 📊 **نظام logging متقدم** للمراقبة والتحليل
- 🧪 **اختبارات شاملة** للجودة والموثوقية
- 📚 **توثيق مفصل** باللغة العربية

### 🚀 البدء الآن
```bash
# للمبتدئين - ابدأ هنا
./enhanced_setup.sh
python3 pattern_examples.py

# للمتقدمين - فحص مباشر
python3 enhanced_bitcoin_scanner.py --pattern low_entropy --duration 30
```

### 🎯 التوصية
**استخدم النسخة المحسنة** للحصول على أفضل النتائج والتجربة الأمثل.

---

## ⚖️ إخلاء المسؤولية

- 🎓 **الغرض التعليمي**: هذا المشروع مخصص للأغراض التعليمية والبحثية فقط
- 📊 **الاحتمالية**: احتمالية العثور على مفاتيح تحتوي على رصيد منخفضة جداً حتى مع التحسينات
- ⚖️ **المسؤولية القانونية**: المطور غير مسؤول عن أي استخدام غير قانوني أو ضار
- 🔒 **الاستخدام الأخلاقي**: استخدم المشروع على مسؤوليتك الخاصة واحترم قوانين بلدك المحلية
- 🛡️ **الأمان**: لا تستخدم مفاتيح تخص أشخاص آخرين أو تنتهك خصوصية الآخرين

---

## 📞 التواصل والدعم

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- 📖 راجع الأدلة المفصلة في الملفات المرفقة
- 🧪 شغل الاختبارات للتشخيص
- 📝 راجع ملفات السجلات للتفاصيل

**المشروع جاهز للاستخدام الفوري! 🚀**

---

*تم تطوير هذا المشروع بعناية فائقة لتوفير أفضل تجربة ممكنة لفحص مفاتيح البيتكوين ضمن الحدود التقنية والقانونية.*
