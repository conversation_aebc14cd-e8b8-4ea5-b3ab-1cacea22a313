# Bitcoin Key Scanner

سكربت Python لتوليد مفاتيح البيتكوين العشوائية والبحث عن العناوين التي تحتوي على رصيد.

## المميزات

- توليد مفاتيح خاصة عشوائية ضمن نطاق محدد
- تحويل المفاتيح إلى عناوين Bitcoin صحيحة
- فحص الرصيد باستخدام blockchain.info API
- حفظ المفاتيح التي تحتوي على رصيد في ملف
- تحديد مدة زمنية للتشغيل
- إحصائيات مباشرة أثناء التشغيل
- معالجة الأخطاء والتحكم في معدل الطلبات

## التثبيت

1. تأكد من وجود Python 3.6+ على نظامك
2. قم بتثبيت المتطلبات:

```bash
pip install -r requirements.txt
```

## الاستخدام

### الاستخدام الأساسي
```bash
python bitcoin_key_scanner.py
```

### خيارات متقدمة
```bash
python bitcoin_key_scanner.py --start 1 --end FFFFF --duration 30 --output my_keys.txt
```

### المعاملات المتاحة

- `--start`: نقطة البداية للنطاق (hex) - افتراضي: 1
- `--end`: نقطة النهاية للنطاق (hex) - افتراضي: أقصى قيمة صحيحة
- `--duration`: مدة التشغيل بالدقائق - افتراضي: 60
- `--output`: ملف حفظ النتائج - افتراضي: found_keys.txt

## أمثلة

### البحث في نطاق صغير لمدة 10 دقائق
```bash
python bitcoin_key_scanner.py --start 1 --end FFFF --duration 10
```

### البحث في نطاق كبير لمدة ساعة
```bash
python bitcoin_key_scanner.py --start 10000 --end FFFFFFFFFF --duration 60
```

## كيف يعمل السكربت

1. **توليد المفتاح الخاص**: يتم توليد رقم عشوائي ضمن النطاق المحدد
2. **إنشاء العنوان العام**: تحويل المفتاح الخاص إلى عنوان Bitcoin صحيح
3. **فحص الرصيد**: استعلام blockchain.info API للتحقق من الرصيد
4. **حفظ النتائج**: إذا وُجد رصيد، يتم حفظ المفتاح والعنوان والرصيد

## تنسيق ملف النتائج

عند العثور على مفتاح يحتوي على رصيد، يتم حفظه بالتنسيق التالي:

```
Timestamp: 2024-01-15 14:30:25
Private Key (Hex): 0x1a2b3c4d5e6f...
Private Key (WIF): L1234567890abcdef...
Address: **********************************
Balance: 0.00123456 BTC
--------------------------------------------------
```

## الأمان والاعتبارات

⚠️ **تحذيرات مهمة:**

1. **الغرض التعليمي**: هذا السكربت مخصص للأغراض التعليمية والبحثية فقط
2. **الاحتمالية المنخفضة**: احتمالية العثور على مفتاح يحتوي على رصيد منخفضة جداً
3. **استهلاك الموارد**: السكربت يستخدم الإنترنت ومعالج الكمبيوتر
4. **حدود API**: يتم تطبيق تأخير بين الطلبات لتجنب حظر IP

## المتطلبات التقنية

- Python 3.6+
- اتصال إنترنت مستقر
- مساحة تخزين لحفظ النتائج

## استكشاف الأخطاء

### خطأ في الاتصال بالإنترنت
```
Error checking balance for [address]: [error details]
```
**الحل**: تأكد من اتصال الإنترنت وأن blockchain.info متاح

### خطأ في تثبيت المكتبات
```
ModuleNotFoundError: No module named 'ecdsa'
```
**الحل**: قم بتشغيل `pip install -r requirements.txt`

### توقف السكربت
- اضغط `Ctrl+C` لإيقاف السكربت بأمان
- سيتم حفظ الإحصائيات النهائية

## إخلاء المسؤولية

هذا السكربت مخصص للأغراض التعليمية والبحثية فقط. المطور غير مسؤول عن أي استخدام غير قانوني أو ضار. استخدم السكربت على مسؤوليتك الخاصة واحترم قوانين بلدك المحلية.
