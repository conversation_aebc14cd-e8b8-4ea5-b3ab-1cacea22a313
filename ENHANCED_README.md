# Enhanced Bitcoin Pattern Scanner
# ماسح البيتكوين المحسن القائم على الأنماط

## نظرة عامة

سكربت Python متقدم لتوليد مفاتيح البيتكوين باستخدام أنماط محددة لزيادة احتمالية العثور على عناوين تحتوي على رصيد. يعمل السكربت بنظام الدفعات (1000 مفتاح لكل دفعة) ويتضمن نظام logging متقدم ومعالجة متوازية.

## المميزات الجديدة

### 🎯 **الأنماط المحسنة**
- **Sequential Keys**: مفاتيح متسلسلة في نطاقات البيتكوين المبكرة
- **Vanity Addresses**: عناوين تبدأ بحروف معينة (1A, 1B, 3A, إلخ)
- **Repeated Digits**: أنماط الأرقام المتكررة
- **Low Entropy**: مفاتيح بإنتروبيا منخفضة (شائعة في البيتكوين المبكر)
- **Birthday Dates**: مفاتيح مبنية على تواريخ الميلاد
- **Common Numbers**: أرقام شائعة ومتسلسلات معروفة
- **Hex Patterns**: أنماط hex محددة

### ⚡ **معالجة الدفعات**
- توليد 1000 مفتاح في كل دفعة
- معالجة متوازية باستخدام ThreadPoolExecutor
- تحكم في عدد العمليات المتزامنة

### 📊 **نظام Logging متقدم**
- سجلات مفصلة لكل مفتاح تم فحصه
- مستويات logging مختلفة (DEBUG, INFO, WARNING, ERROR)
- حفظ السجلات في ملفات منفصلة

### 🔧 **تحسينات الأداء**
- تأخير محسن بين طلبات API (50ms)
- معالجة الأخطاء المحسنة
- إحصائيات مباشرة مفصلة

## التثبيت والإعداد

```bash
# تثبيت المتطلبات
pip3 install -r requirements.txt

# تشغيل الاختبارات
python3 test_patterns.py

# جعل الملفات قابلة للتنفيذ
chmod +x enhanced_bitcoin_scanner.py
chmod +x pattern_examples.py
```

## الاستخدام

### 1. الاستخدام الأساسي

```bash
# فحص بنمط sequential لمدة ساعة
python3 enhanced_bitcoin_scanner.py

# فحص بنمط محدد
python3 enhanced_bitcoin_scanner.py --pattern low_entropy --duration 30

# فحص مع إعدادات مخصصة
python3 enhanced_bitcoin_scanner.py \
    --pattern vanity_1 \
    --batch-size 2000 \
    --duration 60 \
    --workers 8 \
    --log-level DEBUG
```

### 2. الأنماط المتاحة

| النمط | الاحتمالية | الوصف |
|-------|------------|--------|
| `low_entropy` | عالية | مفاتيح بإنتروبيا منخفضة |
| `common_numbers` | عالية | أرقام شائعة ومتسلسلات |
| `vanity_1` | عالية | عناوين تبدأ بـ 1A, 1B, إلخ |
| `sequential` | متوسطة | مفاتيح متسلسلة |
| `repeated_digits` | متوسطة | أنماط أرقام متكررة |
| `birthday_dates` | متوسطة | تواريخ ميلاد شائعة |
| `hex_patterns` | متوسطة | أنماط hex محددة |
| `vanity_3` | متوسطة | عناوين P2SH (تبدأ بـ 3) |

### 3. تشغيل الأمثلة التفاعلية

```bash
# قائمة تفاعلية لاختيار الأنماط
python3 pattern_examples.py

# عرض توضيحي لجميع الأنماط
python3 pattern_examples.py
# ثم اختر "Demo All Patterns"

# اختبار سريع لجميع الأنماط
python3 pattern_examples.py
# ثم اختر "Quick Test All Patterns"
```

### 4. الاستراتيجيات الموصى بها

```bash
# للمبتدئين
python3 enhanced_bitcoin_scanner.py --pattern low_entropy --batch-size 500 --duration 30

# للمستوى المتوسط
python3 enhanced_bitcoin_scanner.py --pattern common_numbers --batch-size 1000 --duration 60

# للمستوى المتقدم
python3 enhanced_bitcoin_scanner.py --pattern sequential --batch-size 2000 --duration 120 --workers 8
```

## المعاملات المتاحة

```bash
--pattern PATTERN        # نوع النمط للاستخدام
--batch-size SIZE        # عدد المفاتيح في كل دفعة (افتراضي: 1000)
--duration MINUTES       # مدة التشغيل بالدقائق (افتراضي: 60)
--output FILE           # ملف حفظ النتائج
--log-level LEVEL       # مستوى السجلات (DEBUG/INFO/WARNING/ERROR)
--workers COUNT         # عدد العمليات المتزامنة (افتراضي: 5)
```

## فهم الأنماط

### 1. Low Entropy (إنتروبيا منخفضة)
```python
# أمثلة على المفاتيح المولدة:
0x123456    # 24 بت
0x12345678  # 32 بت
0x1234567890ABCDEF  # 64 بت
```

### 2. Common Numbers (أرقام شائعة)
```python
# أمثلة:
123456789, 987654321, 111111111
1337, 2020, 2021, 42, 69, 420, 666, 777
```

### 3. Vanity Addresses (عناوين مميزة)
```python
# نطاقات محتملة لإنتاج عناوين تبدأ بـ:
# 1A: 0x1A000000 - 0x1AFFFFFF
# 1B: 0x1B000000 - 0x1BFFFFFF
# 3A: 0x3A000000 - 0x3AFFFFFF
```

### 4. Birthday Dates (تواريخ الميلاد)
```python
# تنسيقات التاريخ:
19901225  # YYYYMMDD
25121990  # DDMMYYYY
12251990  # MMDDYYYY
```

## تحليل النتائج

### تنسيق ملف النتائج
```
Timestamp: 2024-01-15 14:30:25
Pattern Type: low_entropy
Private Key (Hex): 0x123456
Private Key (WIF): L1234567890abcdef...
Address: **********************************
Balance: 0.00123456 BTC
------------------------------------------------------------
```

### ملفات السجلات
```
logs/bitcoin_scanner_20240115_143025.log
```

## الأداء المتوقع

### معدلات الفحص
- **نمط واحد**: 8-15 مفتاح/ثانية
- **معالجة متوازية**: 25-50 مفتاح/ثانية
- **دفعة 1000 مفتاح**: 20-40 ثانية

### استهلاك الموارد
- **الذاكرة**: 100-200 MB
- **المعالج**: متوسط (حسب عدد العمليات)
- **الشبكة**: ~1KB لكل مفتاح

## نصائح للتحسين

### 1. اختيار النمط المناسب
```bash
# للبحث السريع
--pattern low_entropy --batch-size 500

# للبحث الشامل
--pattern sequential --batch-size 2000

# للعناوين المميزة
--pattern vanity_1 --batch-size 1500
```

### 2. تحسين الأداء
```bash
# زيادة العمليات المتزامنة
--workers 10

# تقليل مستوى السجلات
--log-level WARNING

# دفعات أكبر
--batch-size 3000
```

### 3. إدارة الموارد
```bash
# للأجهزة الضعيفة
--workers 3 --batch-size 500

# للخوادم القوية
--workers 15 --batch-size 5000
```

## استكشاف الأخطاء

### مشاكل شائعة

1. **بطء في الفحص**
   ```bash
   # زيادة العمليات المتزامنة
   --workers 8
   
   # تقليل التأخير بين الطلبات (احذر من الحظر)
   # تعديل api_delay في الكود
   ```

2. **استهلاك ذاكرة عالي**
   ```bash
   # تقليل حجم الدفعة
   --batch-size 500
   
   # تقليل العمليات المتزامنة
   --workers 3
   ```

3. **حظر IP من API**
   ```bash
   # زيادة التأخير بين الطلبات
   # تعديل api_delay في الكود إلى 0.1 أو أكثر
   ```

## الأمان والقانونية

### ⚠️ تحذيرات مهمة

1. **الغرض التعليمي**: هذا السكربت للأغراض التعليمية والبحثية فقط
2. **الاحتمالية**: احتمالية العثور على مفتاح يحتوي على رصيد منخفضة جداً
3. **القوانين المحلية**: احترم قوانين بلدك المحلية
4. **استهلاك الموارد**: السكربت يستهلك الإنترنت والمعالج

### أفضل الممارسات

1. **ابدأ بفترات قصيرة** للاختبار
2. **راقب استهلاك الموارد**
3. **استخدم VPN** إذا لزم الأمر
4. **احفظ النتائج بانتظام**

## الدعم والتطوير

### تقارير الأخطاء
- استخدم `test_patterns.py` لتشخيص المشاكل
- تحقق من ملفات السجلات في مجلد `logs/`

### التحسينات المستقبلية
- إضافة APIs متعددة
- دعم العملات الرقمية الأخرى
- واجهة رسومية
- تحليل إحصائي متقدم

---

**إخلاء المسؤولية**: هذا السكربت مخصص للأغراض التعليمية والبحثية فقط. المطور غير مسؤول عن أي استخدام غير قانوني. استخدم على مسؤوليتك الخاصة.
